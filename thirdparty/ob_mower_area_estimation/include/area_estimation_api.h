#ifndef AREA_ESTIMATION_API_H
#define AREA_ESTIMATION_API_H
#pragma once

#include <stdint.h>
#include <vector>
#include <thread>

#define ORBBEC_EXPORTS 1

#if defined(_WIN32) || defined(WIN32)
#if defined(ORBBEC_EXPORTS) || defined(ORBBEC_BUILD_SHARED)
#define ORBBEC_API __declspec(dllexport)
#define ORBBEC_LOCAL
#else
#define ORBBEC_API __declspec(dllimport)
#define ORBBEC_LOCAL
#endif
#else
#if defined(ORBBEC_EXPORTS) || defined(ORBBEC_BUILD_SHARED)
#if __GNUC__ >= 4
#define ORBBEC_API __attribute__((visibility("default")))
#define ORBBEC_LOCAL __attribute__((visibility("hidden")))
#else
#define ORBBEC_API __attribute__((visibility("default")))
#define ORBBEC_LOCAL __attribute__((visibility("default")))
#endif
#endif
#endif

#ifdef __cplusplus
extern "C"
{
#endif
/** define the handle */
#define AREA_ESTIMATION_HANDLE void *

    enum ob_area_estimation_status_output
    {
        area_estimation_success = 0, // 成功估计位姿
        area_imu_init = 1,           // imu初始化中
        area_estimation_error = 2,   // 错误输入，跳过此帧

    };
    typedef struct
    {
        ob_area_estimation_status_output current_status;
        uint64_t timestamp;  // ns
        float quaternion_w;  // qw
        float quaternion_x;  // qx
        float quaternion_y;  // qy
        float quaternion_z;  // qz
        float roll;          // 0
        float pitch;         // 0
        float yaw;           // rad
        float translation_x; // m
        float translation_y; // m
        float translation_z; // m
        float area;          // m*m
        float perimeter;     // m
        int ERROR_CODE;
    } ob_area_estimation_result;

    // int ERROR_CODE;
#define AREA_OK 0x9010 // Area estimate OK 36880

#define AREA_LOG_SAVE_WARNING 0x9011  // log保存警告
#define AREA_ODOM_TIME_WARNING 0x9012 // odom time delay 过大警告
#define AREA_IMU_TIME_WARNING 0x9013  // imu time delay 过大警告
#define AREA_IMU_INIT_WARNING 0x9014  // imu 初始化运动警告

#define AREA_INIT_ERROR 0x9030       // 初始化失败
#define AREA_START_ERROR 0x9031      // 启动失败
#define AREA_ODOM_TIME_ERROR 0x9032  // odom时间错误
#define AREA_ODOM_INPUT_ERROR 0x9033 // odom输入错误
#define AREA_IMU_INPUT_ERROR 0x9034  // imu输入错误
#define AREA_IMU_CONFIG_ERROR 0x9035 // imu配置错误

    typedef struct
    {
        bool saveResult;         // default:0
        bool outputLog;          // default:0
        int useImuMode;          // default:3
        double bodyCompensation; // default:0.165

    } Area_estimation_config;

    /****  OAE: orbbec area estimation  ****/
    typedef void (*ob_area_estimation_result_callback)(const ob_area_estimation_result *result);

    /**
     * @brief 配置回调函数：算法结果
     * @param area_estimation_handle 对象句柄
     * @param proc 回调函数
     */
    ORBBEC_API void OAE_register_result_callback(AREA_ESTIMATION_HANDLE area_estimation_handle, ob_area_estimation_result_callback callback);

    /**
     * @brief 创建系统
     * @param area_estimation_handle 对象句柄
     * @param config_path 配置文件路径及文件名
     * @return 0 for create successfully; RECHARGE_INIT_FAIL for create failure
     */
    ORBBEC_API int32_t OAE_create(AREA_ESTIMATION_HANDLE *area_estimation_handle, const char *config_path);

    /**
     * @brief 获取config信息
     * @param area_estimation_handle 对象句柄
     * @param area_estimation_config 要获取的信标配置
     * @return 1
     */
    ORBBEC_API int32_t OAE_get_config_parameter(AREA_ESTIMATION_HANDLE area_estimation_handle, Area_estimation_config *area_estimation_config);

    /**
     * @brief 设置config信息
     * @param area_estimation_handle 对象句柄
     * @param area_estimation_config 要设置的信标配置
     * @return 1
     * */
    ORBBEC_API int32_t OAE_set_config_parameter(AREA_ESTIMATION_HANDLE area_estimation_handle, Area_estimation_config *area_estimation_config);

    /**
     * @brief 启动系统
     * @param area_estimation_handle 对象句柄
     * @return 0 for start successfully; 1 for recharge_location_handle nullptr;
     *         RECHARGE_SET_DICT_FAIL, RECHARGE_SET_CAM_FAIL
     */
    ORBBEC_API int32_t OAE_start(AREA_ESTIMATION_HANDLE area_estimation_handle);

    /**
     * @brief 释放系统
     * @param area_estimation_handle 对象句柄
     */
    ORBBEC_API void OAE_release(AREA_ESTIMATION_HANDLE area_estimation_handle);

    /**
     * @brief 获得版本号
     * @param area_estimation_handle 对象句柄
     * @param **/
    ORBBEC_API const char *OAE_get_version(AREA_ESTIMATION_HANDLE area_estimation_handle);

    /**
     * @brief 输入 ecode 信息
     * @param area_estimation_handle 对象句柄
     * @param timestamp 时间戳 ns
     * @param v_l left
     * @param v_r right
     * @return 1
     */
    ORBBEC_API int32_t OAE_add_ecode(AREA_ESTIMATION_HANDLE area_estimation_handle,
                                     uint64_t timestamp, double v_l, double v_r);
    /**
     * @brief 输入 imu 信息
     * @param area_estimation_handle 对象句柄
     * @param timestamp 时间戳 ns
     * @param ax ay az acc
     * @param ay az acc
     * @param az acc
     * @param gx gyro
     * @param gy gyro
     * @param gz gyro
     * @return 1
     */
    ORBBEC_API int32_t OAE_add_IMU(AREA_ESTIMATION_HANDLE area_estimation_handle,
                                   uint64_t timestamp, double ax, double ay, double az, double gx, double gy, double gz);

    /**
     * @brief 非线性优化面积
     * @param area_estimation_handle 对象句柄
     */
    ORBBEC_API int32_t OAE_cal_ecode(AREA_ESTIMATION_HANDLE area_estimation_handle);

    /**
     * @brief 清除原数据
     * @param area_estimation_handle 对象句柄
     */
    ORBBEC_API int32_t OAE_clear(AREA_ESTIMATION_HANDLE area_estimation_handle);

#ifdef __cplusplus
}
#endif

#endif // AREA_ESTIMATION_API_H
