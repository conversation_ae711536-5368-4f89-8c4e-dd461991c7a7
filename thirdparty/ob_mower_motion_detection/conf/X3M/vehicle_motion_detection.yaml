%YAML:1.0 # need to specify the file type at the top!

# 0 -- ALL; 1 -- DEBUG; 2 -- INFO; 3 -- WARNING; 4 -- ERROR; 5 -- SILENT
verbosity: 4
resolution: [640, 360]
# 打开显示，默认显示最终光流追踪结果
showImg: false
# 显示光流的追踪中间结果
showOpticalFlow: false

down_sample: 1
frame_skip: 5
maxCorners: 150                # 提取特征的数目
minCoress: 80                  # 少于此数目的特征重新进行提取
pix_shift_threshold: 50        # 像素追踪的最大偏移量阈值

qualityLevel: 0.01             # 特征的特异性参数
minDistance: 10                # 提取特征的最小像素距离
blockSize: 3                   # 提取图像特征窗口
useHarrisDetector: 0           # 是否使用Harris
Harris_k: 0.04                 # Harris检测参数
maxLevel: 3                    # 金字塔层数
winSize: 15                    # 光流窗口大小

minDisplacement: 1.0           # 判断静止时的最小平均像素偏移阈值:0.2 ~ 1 之间
percept_time_diff_thre: 0.2    # 感知结果和图像的时间延时阈值,s
valid_mask_rate: 0.3           # 有效掩码的占比阈值
# true for use the perception result, false for no perception result
use_perception: true
