# 卡滞检测和脱困功能实现说明

## 功能概述

本实现为割草机添加了完整的卡滞检测和脱困功能，能够在各类作业模式（边切、探索、随机割草、螺旋割草）中实时检测机器是否陷入卡滞状态，并在确认卡滞后启动多种脱困策略。

## 核心特性

### 1. 多时间窗口卡滞检测
- **短窗口（30秒）**：快速检测短期卡滞
- **中窗口（2分钟）**：检测中期运动异常
- **长窗口（5分钟）**：检测长期位置停滞
- **分段检测法**：至少两个窗口检测到卡滞才确认，避免误判

### 2. 智能脱困策略
实现了8种脱困策略，按顺序执行：
1. **左转脱困** - 原地左转
2. **右转脱困** - 原地右转  
3. **后退脱困** - 直线后退
4. **前进脱困** - 直线前进
5. **左轮高速脱困** - 左轮高速，右轮低速
6. **右轮高速脱困** - 右轮高速，左轮低速
7. **蠕动模式脱困** - 前进-后退循环运动
8. **轨迹回滚脱困** - 沿历史轨迹回退

### 3. 渐进式功率调整
- 初始使用较低的线速度和角速度
- 每次尝试失败后递增功率
- 设置最大功率限制，确保安全

### 4. 轨迹记录和回滚
- 持续记录机器人运动轨迹（最多1000个点）
- 自动清理过期轨迹点（超过5分钟）
- 支持沿历史轨迹精确回退

## 技术实现

### 数据结构

```cpp
// 卡滞检测状态
enum class StuckDetectionState {
    NORMAL,         // 正常状态
    POTENTIAL_STUCK,// 潜在卡滞
    CONFIRMED_STUCK,// 确认卡滞
    RECOVERING      // 脱困中
};

// 脱困策略
enum class UnstuckStrategy {
    ROTATE_LEFT, ROTATE_RIGHT, MOVE_BACKWARD, MOVE_FORWARD,
    SINGLE_WHEEL_LEFT, SINGLE_WHEEL_RIGHT, 
    WIGGLE_MOTION, TRAJECTORY_ROLLBACK
};

// 轨迹点
struct TrajectoryPoint {
    float x, y, yaw;
    uint64_t timestamp;
};

// 运动窗口
struct MovementWindow {
    uint64_t start_time;
    float total_displacement;
    float total_rotation;
    int window_duration_seconds;
};
```

### 核心算法

#### 卡滞检测算法
```cpp
bool IsStuckDetected() {
    // 检查三个时间窗口的运动量
    bool short_stuck = !CheckMovementInTimeWindow(30);   // 30秒
    bool medium_stuck = !CheckMovementInTimeWindow(120); // 2分钟  
    bool long_stuck = !CheckMovementInTimeWindow(300);   // 5分钟
    
    // 分段检测：至少两个窗口检测到卡滞才确认
    int stuck_count = (short_stuck ? 1 : 0) + (medium_stuck ? 1 : 0) + (long_stuck ? 1 : 0);
    return stuck_count >= 2;
}
```

#### 运动量计算
```cpp
bool CheckMovementInTimeWindow(int window_seconds) {
    // 计算时间窗口内的总位移和旋转
    for (轨迹点) {
        total_displacement += sqrt(dx*dx + dy*dy);
        total_rotation += abs(角度变化);
    }
    
    // 判断是否满足运动阈值
    return (total_displacement > 阈值) || (total_rotation > 阈值);
}
```

### 关键参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| stuck_displacement_threshold_ | 0.1m | 位移阈值 |
| stuck_rotation_threshold_ | 0.1rad | 旋转阈值 |
| unstuck_linear_speed_ | 0.15m/s | 初始脱困线速度 |
| unstuck_angular_speed_ | 0.3rad/s | 初始脱困角速度 |
| max_unstuck_attempts_ | 8 | 最大脱困尝试次数 |
| trajectory_rollback_distance_ | 0.5m | 轨迹回滚距离 |

## 集成方式

### 1. 在现有打滑检测线程中集成
```cpp
void SlipDetectionThread() {
    while (running) {
        // 原有打滑检测
        bool current_slip = IsWheelSlipping(...);
        
        // 新增卡滞检测
        bool is_stuck = IsStuckDetected();
        if (is_stuck && stuck_state_ != RECOVERING) {
            HandleStuckRecovery();
        }
    }
}
```

### 2. 轨迹记录集成
```cpp
void SetMotorSpeedData(const MotorSpeedData &data) {
    // 基于电机速度估算位置变化
    // 每100ms更新一次轨迹记录
    if (时间间隔 >= 100ms) {
        UpdateTrajectoryRecord(x, y, yaw);
    }
}
```

## 安全机制

1. **线程安全**：使用互斥锁保护共享数据
2. **功率限制**：设置最大脱困速度，防止过激动作
3. **超时保护**：每个脱困策略最多执行10秒
4. **尝试次数限制**：最多8次脱困尝试，防止无限循环
5. **异常上报**：脱困失败时上报异常状态

## 使用示例

```cpp
// 初始化（在构造函数中自动完成）
NavigationMowerAlg mower(param);

// 运行时自动检测和脱困
// 无需手动调用，集成在现有的运行循环中

// 手动重置（如需要）
mower.ResetStuckDetection();
```

## 日志输出

系统会输出详细的调试信息：
- `[StuckDetection]` - 卡滞检测相关日志
- `[StuckRecovery]` - 脱困操作相关日志  
- `[TrajectoryRollback]` - 轨迹回滚相关日志

## 性能影响

- **CPU占用**：轻微增加，主要在20ms检测周期内
- **内存占用**：约增加40KB（1000个轨迹点）
- **实时性**：不影响主控制循环，脱困在独立线程执行

## 扩展性

该实现具有良好的扩展性：
1. 可轻松添加新的脱困策略
2. 可调整检测参数适应不同环境
3. 可集成更精确的定位系统（如GPS、SLAM）
4. 可添加机器学习算法优化策略选择

## 测试验证

提供了完整的测试程序 `test_stuck_detection.cpp`，验证：
- 正常运动场景
- 卡滞检测场景  
- 脱困策略执行

运行测试：
```bash
g++ -std=c++17 -o test_stuck_detection test_stuck_detection.cpp
./test_stuck_detection
```
