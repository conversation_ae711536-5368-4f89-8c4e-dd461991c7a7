#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct NavigationEdgeFollowAlgConfig
{
    std::string edge_follow_alg_type{"occupancy_grid"};
    std::string flAction_type{"straight_right"};
    bool vel_smooth_enable{false};

    float edge_follow_linear{0.2};
    float edge_follow_angular{0.3};

    int dividing_line_left{198};
    int dividing_line_right{300};

    int max_obstacle_avoid_duration_times{500}; // 避障走直线持续发送速度次数
    int max_turn_right_times{500};              // 右转次数最大次数
    float min_vel_angular{0.05};                // 最小有效角速度
    int max_zero_vel_times{10};

    // 定义感兴趣区域(ROI)，以矩形的左上角点（x, y）和宽度、高度
    int x{2};        // 左上角点的x坐标
    int y{160};      // 左上角点的y坐标
    int width{396};  // 矩形区域的宽度
    int height{196}; // 矩形区域的高度

    // occupancy grid parameters
    float acc{0.005};      // 0.0125,0.015
    float slow_acc{0.032}; // 0.02
    float kp_r{0.125};
    float kp_l{0.125}; // 0.125

    float wheel_base{0.4};
    int left_line_x{11};
    int right_line_x{36}; // 28
    float resolution{0.025};
    float look_ahead_dis{0.5};
    float danger_dis{0.3};
    int grid_width{40};
    int grid_height{36};
    float v_max{0.3};   // 0.25
    int max_fl_time{3}; // 20000
    float fl_back_right_target_dis{-0.15};
    float fl_turn_right_target{0.53}; // 1.4

    float fl_forward_target_dis{0.58}; // 0.48
    float fl_forward_r_target_dis{0.30};
    float fl_go_straight_linear{0.2};
    float fl_go_straight_angluar{0.3};
    float fl_turn_r_linear{0.05};
    float fl_turn_r_angular{0.2};

    int max_ao_turn_l_spot_num{2000};
    int max_ao_turn_l_num{2000};
    float ao_turn_l_spot_angular{0.5};
    float ao_turn_l_wheel_r{0.05};
    float ao_turn_l_wheel_l{0.2};
    int max_ao_time{20000};

    float fl_spot_turn_r_angular{0.3};
    float fl_turn_right_target_new{1.05};
    float fl_tuning_target{0.3};
    float fl_tuning_linear{0.15};
    float fl_tuning_angular{0.2}; // 0.5
    float fl_turn_r_angular_new{0.5};

    // 其它算法配置文件
    std::string predict_trajectory_conf{"conf/navigation_common/navigation_predict_trajectory.yaml"};
    std::string velocity_smooth_conf{"conf/navigation_common/navigation_velocity_smooth.yaml"};
    std::string path_track_conf{"conf/navigation_common/navigation_path_track.yaml"};

    NavigationEdgeFollowAlgConfig() = default;
    ~NavigationEdgeFollowAlgConfig() = default;
    NavigationEdgeFollowAlgConfig(const NavigationEdgeFollowAlgConfig &config) = default;
    NavigationEdgeFollowAlgConfig &operator=(const NavigationEdgeFollowAlgConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationEdgeFollowAlgConfig &lhs, const NavigationEdgeFollowAlgConfig &rhs);
bool operator!=(const NavigationEdgeFollowAlgConfig &lhs, const NavigationEdgeFollowAlgConfig &rhs);

} // namespace fescue_iox
