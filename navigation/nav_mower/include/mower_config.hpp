#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct NavigationMowerAlgConfig
{
    // 信标调度 - 出桩
    bool is_enable_unstake_mode{true}; // 是否开启出桩模式 /*param*/
    float unstake_distance{1.0};       // 出桩距离 /*param*/
    float unstake_adjust_yaw{0.52};    // 出桩调整角度 /*param*/
    float unstake_vel_linear{0.2};     // 出桩线速度 /*param*/
    float unstake_vel_angular{0.5};    // 出桩角速度 /*param*/

    // 算法参数
    float mower_linear{0.2};  /*param*/
    float mower_angular{0.5}; /*param*/
    // int perception_drive_cooldown_time{5};   // 感知驱动冷却时间 30s /*param*/
    int edge_mode_direction{-1};             // 默认逆时针 -1 /*param*/
    float cross_region_adjust_yaw{1.57};     // 跨区域后调整方位角 /*param*/
    float cross_region_adjust_displace{0.3}; // 跨区域后调整位移 /*param*/
    float mark_distance_threshold{0.5};      // 1.0/1.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    float camera_2_center_dis{0.37};         // 小车摄像头到旋转中心的距离为0.45 /*param*/

    int edge_perception_drive_cooldown_time_threshold{10}; // 10s 沿边感知驱动冷却时间  /*param*/
    int qr_detection_cooldown_time_threshold{30};          // 60s 沿边感知驱动冷却时间  /*param*/
    int mark_detection_cooldown_time_threshold{30};        // 60s 沿边感知驱动冷却时间  /*param*/

    // 区域探索
    float recharge_distance_threshold{1.2}; // 区域探索回充距离阈值/*param*/

    // 割草前处理
    float mower_start_qr_distance_threshold{0.5}; // 割草前与二维码的距离阈值/*param*/

    NavigationMowerAlgConfig() = default;
    ~NavigationMowerAlgConfig() = default;
    NavigationMowerAlgConfig(const NavigationMowerAlgConfig &config) = default;
    NavigationMowerAlgConfig &operator=(const NavigationMowerAlgConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationMowerAlgConfig &lhs, const NavigationMowerAlgConfig &rhs);
bool operator!=(const NavigationMowerAlgConfig &lhs, const NavigationMowerAlgConfig &rhs);

} // namespace fescue_iox
