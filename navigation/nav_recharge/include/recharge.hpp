#ifndef NAVIGATION_RECHARGE_HPP
#define NAVIGATION_RECHARGE_HPP

#include "data_type.hpp"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "mower_msgs/msg/mcu_sensor.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "opencv2/opencv.hpp"
#include "velocity_publisher.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct RechargeAlgParam
{
    int try_max_num{2};                    // 重复探索最大次数
    int charge_status_max_num{2};          // 充电桩状态保存最大次数
    int no_qr_max_num{30};                 // 记录得不到QR码最大次数
    int save_data_num{5};                  // 保存数据帧数
    int save_terminal_num{10};             // 保存充电桩端子数据帧数
    int head_center_min_dist{45};          // 充电桩头像素偏差
    int head_center_max_dist{275};         // 回充充电桩可调像素距离阈值
    int station_qr_direction_min_dist{30}; // 二维码检测中心和充电桩检测中心相对方位最小像素阈值
    int img_width_dist{50};                // 检测框宽度像素距离阈值

    float qr_code_clear_angle{1.13};     // 1.13(65度)
    float qr_code_detect_angle_1{0.0};   // 0.2618(15度), 0.1745(10度)，0.0873(5度)
    float qr_code_min_distance{0.65};    // 离二维码最近距离 0.65 m
    float start_recharge_distance{1.75}; // 开始回充距离 1.5 m
    float qr_code_x_min_dist{1.12};      // 调整y和yaw时x的最大距离阈值 1.07 m
    float qr_code_y_min_dist{0.04};
    float circle_r_dist{1.0}; // 转弯半径最大距离阈值 1.2 m
    float explore_distance{0.075};
    float explore_vel{0.075};
    float kp_y{3.0};                    // PID变量kp
    float kp_yaw{3.0};                  // PID变量kp_yaw
    float kp_perception{-0.004};        // PID变量kp_perception
    float ki_y{0.015};                  // PID变量ki_y
    float ki_yaw{0.015};                // PID变量ki_yaw
    float recharge_adjust_linear{0.25}; // 回充充电桩调整线速度
    float recharge_pile_linear{0.1};    // 上桩初始阶段线速度
    float recharge_pile_angular{0.25};  // 上桩角速度
};

struct RechargeAlgResult
{
    bool recharge_completed{false};
    bool result{true};
    RechargeAlgResult(bool recharge_completed = false, bool result = true)
        : recharge_completed(recharge_completed)
        , result(result)
    {
    }
};

class NavigationRechargeAlg
{
    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;

public:
    NavigationRechargeAlg(const RechargeAlgParam &param);
    ~NavigationRechargeAlg();
    RechargeAlgResult DoRecharge(const MarkLocationResult &mark_loc_result, const CrossRegionRunningState &cross_region_state, const ChargeStationDetectResult &station_result, const QRCodeLocationResult &qrcode_result, McuExceptionStatus &mcu_exception_status);
    void ProhibitVelPublisher();
    void SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback);
    void SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback);
    void SetRechargeAlgParam(const RechargeAlgParam &param);
    void ResetRechargeFlags(bool dock_status);
    void ResetCrossRegionFlags();
    void ResetOtherStates();
    void SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result);
    void SetChargeStationResult(const ChargeStationDetectResult &station_result);
    void SetMCUSensor(const mower_msgs::msg::McuSensor &data);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    void SetAlgoRunningState(MowerRunningState state);
    void UpdateFeatureSelection(const ThreadControl &thread_control);
    void SetRechargeRunningStateCallback(std::function<void(RechargeRunningState)> callback);
    const char *GetVersion();
    bool single_region_recharge_flag_{false};

private:
    void InitPublisher();
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void UpdateRechargeRunningState(RechargeRunningState state);
    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishZeroVelocity();
    void PauseVelocity();
    void ResumeVelocity();

    void DealFeatureSelect(ThreadControl control, bool state);
    void EdgeFollowDisable();
    void EdgeFollowEnable();
    void RandomMowerDisable();
    void RandomMowerEnable();
    void CrossRegionDisable();
    void CrossRegionEnable();

    bool ProcessRechargeRotate(const ChargeStationDetectResult &station_result, const QRCodeLocationResult &qrcode_result);

    std::vector<float> Process_QRdata(std::vector<float> qr_x_set, std::vector<float> qr_y_set, std::vector<float> qr_yaw_set);

    std::vector<float> Collect_QRdata(const ChargeStationDetectResult &charge_station_detect_result);
    bool Adjust_TerminalData();

    float Fliter_QRdata(std::deque<float> &qr_data_set);
    float LimitAngleVel(float kp, float error);
    float RobottoCameraTF(float qr_yaw, float qr_y);

    bool ProcessQRCodeDetect(const ChargeStationDetectResult &charge_station_detect_result, const QRCodeLocationResult &qr_code_detect_result, McuExceptionStatus &mcu_exception_status);
    void ProcessQRCodeDetectNoPose();
    void OneStageAdjust(float first_turn_flag, float second_turn_flag, float qr_yaw);
    void TurnObtuseAdjust(float first_turn_flag, float second_turn_flag, float third_turn_flag, float qr_x, float qr_y, float qr_yaw);
    void TurnAcuteAdjust(float first_turn_flag, float second_turn_flag, float third_turn_flag, float qr_x, float qr_y, float qr_yaw);
    bool ProcessQRCodeDetectHavePose(const ChargeStationDetectResult &charge_station_detect_result, McuExceptionStatus &mcu_exception_status, float qr_yaw, float qr_x, float qr_y);
    void AdjustYAndYawError(float first_turn_flag, float second_turn_flag, float third_turn_flag, float qr_x, float qr_y, float qr_yaw);
    bool ProcessQRCodeDetectNoQRCode(const ChargeStationDetectResult &charge_station_detect_result, McuExceptionStatus &mcu_exception_status);
    void Record_Stable_QRdata(const ChargeStationDetectResult &charge_station_detect_result);
    void SendRechargeRunningState();
    bool ProcessBeaconRechargeDetection(const MarkLocationResult &mark_loc_result);
    void CrossRegionBeaconAdjust(const CrossRegionRunningState &cross_region_state);
    void SingleAreaStateAdjust(const ChargeStationDetectResult &station_result,
                               const QRCodeLocationResult &qrcode_result);
    void MultiAreaStateAdjust(bool &is_beacon_valid);
    void AdjustRotate();
    bool ProcessDock();
    void ProcessNoQRCodeAndEdgeFollow();
    void ProcessNoQRCodeInDetectProcess(const ChargeStationDetectResult &station_result);
    bool ProcessNoQRCodeAndPerceptionRecharge(const ChargeStationDetectResult &charge_station_detect_result, McuExceptionStatus &mcu_exception_status);
    bool ProcessNoQRCodeAndRotateRecharge(const ChargeStationDetectResult &charge_station_detect_result, const QRCodeLocationResult &qrcode_result, McuExceptionStatus &mcu_exception_status);
    bool Final_Recharge_Opt(McuExceptionStatus &mcu_exception_status);

    void ProcessBeaconRechargeDetection(const MarkLocationResult &mark_loc_result, bool &is_beacon_valid);
    void HandleEdgePerceptionBeaconRechargeDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active);
    void FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx);
    void PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result);
    void HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                     std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold);
    bool HandleRechargeCrossRegionStates(const CrossRegionRunningState &cross_region_state);
    void UpdateCrossRegionRunningState(CrossRegionRunningState state);
    void ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular);
    void ControlLinearMotion(const float &pass_point, const float &location, const float &vel_linear, const int &reverse);
    int PairNumber(int n);

private:
    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};
    std::function<void(CrossRegionRunningState)> cross_region_running_state_callback_;
    std::function<void(const std::vector<FeatureSelectData> &)> feature_select_callback_;
    std::function<void(RechargeRunningState)> recharge_running_state_callback_;

    std::ofstream out_file;
    std::string qr_data_path = "/userdata/yueqian/qr_data.txt";

    std::unique_ptr<iox_exception_publisher> pub_exception_;

    // 运行状态量
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-未开始，1-运行中，2-暂停

    uint64_t perception_straight_driving_time_{12000};
    uint64_t straight_driving_time_{6000};
    uint64_t collision_drving_time_{3000};
    uint64_t detect_stay_time_{15000};
    uint64_t straight_dock_time_{4000};
    uint64_t rotate_edge_following_time_{6000};
    uint64_t backward_time_{8000};
    uint64_t stay_all_time_{3000};
    uint64_t stay_time_{50};
    int try_max_num_{2};                    // 重复探索最大次数
    int charge_status_max_num_{2};          // 充电桩状态保存最大次数
    int no_qr_max_num_{30};                 // 记录得不到QR码最大次数
    int save_data_num_{5};                  // 保存数据帧数
    int save_terminal_num_{10};             // 保存充电桩端子数据帧数
    int save_terminal_time_{1000};          // 保存充电桩端子数据帧持续时间
    int save_head_detect_num_{15};          // 保存充电桩头检测结果的数据帧数
    int head_center_min_dist_{45};          // 充电桩头最小像素偏差
    int head_center_max_dist_{275};         // 回充充电桩可调像素距离阈值
    int station_qr_direction_min_dist_{30}; // 二维码检测中心和充电桩检测中心相对方位最小像素阈值
    int img_width_dist_{50};                // 检测框宽度像素距离阈值

    float qr_code_clear_angle_{1.13};     // 1.13(65度)
    float qr_code_detect_angle_1_{0.0};   // 0.2618(15度), 0.1745(10度)，0.0873(5度)
    float qr_code_min_distance_{0.65};    // 离二维码最近距离 0.65 m
    float start_recharge_distance_{1.75}; // 开始回充距离 1.5 m
    float qr_code_x_min_dist_{1.12};      // 调整y和yaw时x的最大距离阈值 0.92 m
    float qr_code_y_min_dist_{0.04};
    float circle_r_dist_{1.0}; // 转弯半径最大距离阈值 1.2 m
    float explore_distance_{0.075};
    float explore_vel_{0.075};
    float kp_y_{3.0};                        // PID变量kp_y
    float kp_yaw_{3.0};                      // PID变量kp_yaw
    float kp_perception_{-0.004};            // PID变量kp_perception
    float ki_y_{0.015};                      // PID变量ki_y
    float ki_yaw_{0.015};                    // PID变量ki_yaw
    float recharge_adjust_linear_{0.25};     // 回充充电桩调整线速度
    float recharge_max_adjust_angular_{0.3}; // 回充充电桩头对准调整最大角速度
    float recharge_pile_linear_{0.1};        // 上桩初始阶段线速度
    float recharge_pile_angular_{0.25};      // 上桩角速度
    float i_y_{0};
    float i_yaw_{0};

    uint64_t rotate_begin_time_{0};
    uint64_t detect_begin_time_{0};
    uint64_t straight_begin_time_{0};
    uint64_t collision_begin_time_{0};
    uint64_t perception_straight_begin_time_{0};
    uint64_t detect_time_{0};
    uint64_t rotate_time_{0};
    uint64_t straight_time_{0};
    uint64_t perception_straight_time_{0};
    uint64_t collision_time_{0};
    uint64_t pre_timestamp_ms_{0};
    uint64_t pre_station_timestamp_ms_{0};
    uint64_t pre_qr_timestamp_ms_{0};
    uint64_t pre_perception_timestamp_ms_{0}; // 时间戳

    bool save_qr_data_{false};
    bool save_terminal_data_{false};
    bool save_station_data_{false};
    bool charge_current_terminal_status_{false}; // 是否接触上充电桩
    bool charge_terminal_status_{false};         // 是否接触上充电桩
    bool head_direction_flag_{true};             // 充电桩头标识
    bool is_move_to_station_complete_{false};    // 回充充电桩检测过程执行标识
    bool is_qr_code_complete_{false};            // 回充二维码检测过程执行标识
    bool drvie_doing_time_{false};               // 运动过程执行标识
    bool qr_code_direction_is_ok_{false};
    bool qr_code_y_is_ok_{false};
    bool qr_code_x_is_ok_{false};
    bool dock_success_{true};     // 对桩失败标志位
    bool collision_occur_{false}; // 碰撞失败标志位
    bool cross_region_flag_{false};
    bool perception_recharge_flag_{false}; // 感知上桩标志位
    bool rotate_recharge_flag_{false};     // 感知上桩标志位
    bool station_result_is_head_{false};

    int record_num_{0};           // 记录二维码次数
    int no_qr_num_{0};            // 记录得不到QR码次数
    int record_stable_x_data_{0}; // 记录稳定对齐x版本标志位
    int dock_num_{0};             // 对桩次数
    int adjust_num_{0};           // 调节次数
    int head_center_error_{0};    // 检测框宽度像素距离阈值

    float stable_x_{0.0};
    float stable_y_{0.0};
    float stable_w_{0.0};
    float pre_x_{0.0};

    float odom_time{0.0};

    std::mutex qr_detect_mutex_;
    std::mutex charge_detect_mutex_;
    std::vector<float> qr_detect_x_;
    std::vector<float> qr_detect_y_;
    std::vector<float> qr_detect_yaw_;
    std::vector<int> terminal_data_;
    std::vector<int> charge_station_head_data_;
    std::vector<int> no_qr_charge_station_head_data_;
    std::deque<float> save_qr_x_tmp_, save_qr_y_tmp_, save_qr_w_tmp_;
    bool is_qr_code_detected_{false}; // 二维码检测标志位

    // 回充运行变量
    RechargeRunningState recharge_state_{RechargeRunningState::UNDEFINED};
    std::mutex recharge_mutex_;
    std::mutex terminal_mtx_;

    // 二维码跨区域变量
    std::chrono::steady_clock::time_point last_cooldown_time_; // 用于记录冷却开始时间
    std::chrono::seconds edge_perception_drive_duration_{0};
    std::chrono::steady_clock::time_point last_mark_detection_time_; // 上次检测到充电桩二维码的时间
    std::chrono::seconds mark_detection_duration_{0};
    ThreadControl thread_control_{ThreadControl::UNDEFINED}; // 控制其它任务
    int edge_perception_drive_cooldown_time_threshold_{10};  // 10s 沿边感知驱动冷却时间  /*param*/
    int qr_detection_cooldown_time_threshold_{30};           // 60s 沿边感知驱动冷却时间  /*param*/
    int mark_detection_cooldown_time_threshold_{30};         // 60s 沿边感知驱动冷却时间  /*param*/

    float mower_linear_{0.2};                 /*param*/
    float mower_angular_{0.5};                /*param*/
    float cross_region_adjust_yaw_{1.57};     // 跨区域后调整方位角 /*param*/
    float cross_region_adjust_displace_{0.3}; // 跨区域后调整位移 /*param*/
    float mark_distance_threshold_{0.5};      // 0.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    float camera_2_center_dis_{0.37};         // 小车摄像头到旋转中心的距离为切尔西(0.37) 格力博(0.45) /*param*/
    bool is_cooldown_active_ = false;         // 用于控制冷却机制是否激活
    bool is_in_cooldown_time_ = false;        // 用于标记是否在冷却时间内
    BeaconStatus beacon_status_;
    bool is_first_enter_last_mark_detection_time_ = true;
    bool first_detection_beacon_ = true; // 首次检测信标
    bool cross_to_recharge_ = false;     // 用于控制冷却机制是否激活
    int next_paired_beacon_id_ = -1;     // 下一对信标id
    int edge_mode_direction_{-1};        // 默认逆时针 -1/*param*/
    int current_mark_id_{-1};
    int region_count_{1};
};

} // namespace fescue_iox

#endif