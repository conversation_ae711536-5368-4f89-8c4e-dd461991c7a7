#ifndef NAV_DATA_TYPE_HPP
#define NAV_DATA_TYPE_HPP

#include "opencv2/opencv.hpp"
#include "utils/math_type.hpp"

#include <cstdint>

namespace fescue_iox
{

enum class McuExceptionStatus : int
{
    NORMAL = 0,    // 正常情况
    COLLISION = 1, // 碰撞异常
    LIFTING = 2,   // 抬升异常
    UNKNOWN = -1   // 未知状态
};

// 定义方向枚举量
enum class Direction : int
{
    UNDEFINED = -1, // 未定义
    LEFT_TURN = 0,  // 左转
    RIGHT_TURN = 1  // 右转
};

// 定义所在信标的进出状态
enum class Beaconstatus : int
{
    ENTRY = 0,    // 进通道
    EXIT = 1,     // 出通道
    UNDEFINED = 2 // 未定义
};

enum class ThreadControl : int
{
    PERCEPTION_EDGE_THREAD = 0, // 感知沿边进程
    CROSS_REGION_THREAD = 1,    // 跨区域进程
    RECHARGE_THREAD = 2,        // 回充进程
    RANDOM_MOWING_THREAD = 3,   // 随机割草进程
    BEHAVIOR_THREAD = 4,
    SPIRAL_MOWING_THREAD = 5,
    CUT_BORDER_THREAD = 6,
    ESCAPE_THREAD = 7,
    CLOSE_ALL_TASK,
    UNDEFINED // 未定义
};

// 恢复状态
enum class BehaviorRunningState : int
{

    SUCCESS = 0, // 恢复节点执行成功
    FAILURE,     // 恢复节点执行失败
    RUNNING,     // 恢复节点执行进行中
    UNDEFINED    // 未定义
};

// 跨区域状态
enum class CrossRegionRunningState : int
{
    EDGE_FINDING_BEACON = 0,                 // 新增沿边找信标状态 /**该状态允许切换状态 */ /**可以采取恢复模式 */
    PER_FOUND_BEACON,                        // 感知已经找到信标 /**该状态允许切换状态 */ /**可以采取恢复模式 */
    STAGE3_LOC_DETECT_BEACON_WITH_POS,       // 第三阶段，定位检测到二维码，可以算出位姿 /**不允许切换状态 */ /**不可以采取恢复模式 */
    STAGE4_ENTERING_CHANNEL,                 // 第四阶段，刚入通道，且已直行一定距离 /**不允许切换状态 */ /**不可以采取恢复模式 */
    STAGE4_LOC_NO_DETECT_BEACON,             // 第四阶段，定位未检测到二维码 /**不允许切换状态 */ /**不可以采取恢复模式 */
    STAGE4_NON_GRASS_REACHED,                // 第四阶段，非草地到达 /**不允许切换状态 */ /**不可以采取恢复模式 */
    STAGE4_LOC_DETECT_BEACON_NO_POS,         // 第四阶段，定位检测到二维码，不能算出位姿 /**不允许切换状态 */ /**不可以采取恢复模式 */
    STAGE4_LOC_DETECT_BEACON_WITH_POS,       // 第四阶段，定位检测到二维码，可以算出位姿 /**不允许切换状态 */ /**不可以采取恢复模式 */
    STAGE4_BEACON_EXIT_CROSS_REGION,         // 第四阶段，信标检测退出跨区域 /**不允许切换状态 */ /**不可以采取恢复模式 */
    STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION, // 第四阶段，非草地到草地退出跨区域 /**不允许切换状态 */ /**不可以采取恢复模式 */
    UNDEFINED                                // 未定义 /**该状态允许切换状态 */ /**可以采取恢复模式 */
};

inline constexpr const char *asStringLiteral(const CrossRegionRunningState state) noexcept
{
    switch (state)
    {
    case CrossRegionRunningState::EDGE_FINDING_BEACON:
        return "CrossRegionRunningState::EDGE_FINDING_BEACON";
    case CrossRegionRunningState::PER_FOUND_BEACON:
        return "CrossRegionRunningState::PER_FOUND_BEACON";
    case CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS:
        return "CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS";
    case CrossRegionRunningState::STAGE4_ENTERING_CHANNEL:
        return "CrossRegionRunningState::STAGE4_ENTERING_CHANNEL";
    case CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON:
        return "CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON";
    case CrossRegionRunningState::STAGE4_NON_GRASS_REACHED:
        return "CrossRegionRunningState::STAGE4_NON_GRASS_REACHED";
    case CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS:
        return "CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS";
    case CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS:
        return "CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS";
    case CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION:
        return "CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION";
    case CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION:
        return "CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION";
    case CrossRegionRunningState::UNDEFINED:
        return "CrossRegionRunningState::UNDEFINED";
    }
    return "CrossRegionRunningState::UNDEFINED";
}

// 回充状态
enum class RechargeRunningState : int
{
    EDGE_FINDING_QR_CODE = 0, // 新增沿边找充电桩二维码 /**该状态允许切换状态 */ /**可以采取恢复模式 */
    PER_FOUND_QR_CODE,        // 感知定位已经找到充电桩二维码 /**不允许切换状态 */ /**不可以采取恢复模式 */
    PROCESS_FOUND_QR_CODE,
    UNDEFINED // 未定义 /**该状态允许切换状态 */ /**可以采取恢复模式 */
};

inline constexpr const char *asStringLiteral(const RechargeRunningState state) noexcept
{
    switch (state)
    {
    case RechargeRunningState::EDGE_FINDING_QR_CODE:
        return "RechargeRunningState::EDGE_FINDING_QR_CODE";
    case RechargeRunningState::PER_FOUND_QR_CODE:
        return "RechargeRunningState::PER_FOUND_QR_CODE";
    case RechargeRunningState::PROCESS_FOUND_QR_CODE:
        return "RechargeRunningState::PROCESS_FOUND_QR_CODE";
    case RechargeRunningState::UNDEFINED:
        return "RechargeRunningState::UNDEFINED";
    }
    return "RechargeRunningState::UNDEFINED";
}

enum class NavAlgCtrlState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class RechargeState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class CrossRegionState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class EdgeFollowState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class RandomMowerState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class MowerState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class RandomMowerRunningState : int
{
    STOP = 0,
    RUNNING = 1,
    FINISH = 2
};

enum class MowerRunningState : int
{
    STOP = 0,
    RUNNING = 1,
    PAUSE = 2,
    UNDEFINED,
};

inline constexpr const char *asStringLiteral(const MowerRunningState state) noexcept
{
    switch (state)
    {
    case MowerRunningState::STOP:
        return "MowerRunningState::STOP";
    case MowerRunningState::RUNNING:
        return "MowerRunningState::RUNNING";
    case MowerRunningState::PAUSE:
        return "MowerRunningState::PAUSE";
    }
    return "MowerRunningState::UNDEFINED";
}

using BeaconStatusPair = std::pair<Beaconstatus, Beaconstatus>;
struct BeaconStatusPairMap
{
    BeaconStatusPair beacon_status_pair;
    int mark_id;
    int beacon_look_count = 0; // 信标看见次数

    // 重载==运算符
    bool operator==(const BeaconStatusPairMap &other) const
    {
        return this->mark_id == other.mark_id;
    }
};

struct BeaconStatus
{
    int mark_id = -1;
    int beacon_look_count = 0; // 信标看见次数

    BeaconStatus() = default;
    BeaconStatus(int mark_id)
        : mark_id(mark_id)
    {
    }
    BeaconStatus(int mark_id, int beacon_look_count)
        : mark_id(mark_id)
        , beacon_look_count(beacon_look_count)
    {
    }
    BeaconStatus(const BeaconStatus &other)
        : mark_id(other.mark_id)
        , beacon_look_count(other.beacon_look_count)
    {
    }

    // 重载==运算符
    bool operator==(const BeaconStatus &other) const
    {
        return this->mark_id == other.mark_id;
    }
};

// 草地检测状态
enum class GrassDetectStatus : int
{
    NO_GRASS = 0,                // 无草地（全是障碍物）
    HAVE_GRASS_NO_OBSTACLE = 1,  // 有草地无障碍物 (全是草地)
    HAVE_GRASS_HAVE_OBSTACLE = 2 // 有草地有障碍物 （部分草地部分障碍物）
};

enum class ObstacleDetectStatus : int
{
    NO_OBSTACLE = 0,  // 无障碍物
    HAVE_OBSTACLE = 1 // 有障碍物
};

enum class PositionOfGrass : int
{
    INVALID = -1,    // 无效
    OUTER_GRASS = 0, // 草地外
    INNER_GRASS = 1  // 草地内
};

// 边界走向枚举
enum class BoundaryDirection : int
{
    NO_DIRECTION = -1,  // 走向无效
    LEFT_DIRECTION = 0, // 走向朝左边
    RIGHT_DIRECTION = 1 // 走向朝右边
};

// 0 表示当前点处于草地外部，1 表示当前点处于草地内部, -1 表示结果无效
// 结构体中每个成员变量与图像中的点对应关系：从左到右一一对应。
struct BoundaryStatus
{
    PositionOfGrass flag1{PositionOfGrass::INVALID};
    PositionOfGrass flag2{PositionOfGrass::INVALID};
    PositionOfGrass flag3{PositionOfGrass::INVALID};
    PositionOfGrass flag4{PositionOfGrass::INVALID};
};

struct FeatureSelectData
{
    ThreadControl alg_id{0};
    int alg_status{-1};
    FeatureSelectData()
    {
    }
    FeatureSelectData(ThreadControl alg_id, int alg_status)
        : alg_id(alg_id)
        , alg_status(alg_status)
    {
    }
};

// 距离边界状态
enum class BoundaryDistanceStatus : int
{
    CLOSE = 0,  // 距离近
    MIDDLE = 1, // 距离适中
    FAR = 2     // 距离远
};

// 定义一个简单的Point结构体
struct Point
{
    int x, y;
    // 构造函数
    Point(int x = 0, int y = 0)
        : x(x)
        , y(y)
    {
    }
};

// 定义表示向量的结构体
struct Vector
{
    Point start; // 起点，靠近割草机位置的点作为起点
    Point end;   // 终点，远离割草机位置的点作为终点
};

struct VelocityData
{
    float linear{0};      // 线速度
    float angular{0};     // 角速度
    uint64_t duration{0}; // 持续时间
};

struct InversePerspectImage
{
    uint32_t width;
    uint32_t height;
    uint32_t size;
    cv::Mat image;
    float pixels_to_meters;
    std::vector<cv::Point> path;
};

// 边界检测结果
struct BoundaryResult
{
    // 割草机逆透视投影后的安全线内的mask
    InversePerspectImage inverse_perspect_mask;
};

struct ObstacleResult
{
    // 0-未检测到边界，1-检测到边界
    ObstacleDetectStatus left_obstacle_status{ObstacleDetectStatus::NO_OBSTACLE};
    // 0-未检测到边界，1-检测到边界
    ObstacleDetectStatus ahead_obstacle_status{ObstacleDetectStatus::NO_OBSTACLE};
    // 0-未检测到边界，1-检测到边界
    ObstacleDetectStatus right_obstacle_status{ObstacleDetectStatus::NO_OBSTACLE};

    // 障碍物最近距离
    float obstacle_distance{0};
    // 控制量
    VelocityData velocity_result;
};

struct OccupancyResult
{
    int16_t width;
    int16_t height;
    float resolution;
    std::vector<uint8_t> cells_array;
    std::vector<std::vector<uint8_t>> grid;
};

// 感知结果
struct PerceptionFusionResult
{
    // 0-未检测到草地，1-检测到草地但未检测到障碍物，2-检测到草地和障碍物
    GrassDetectStatus grass_detecte_status{GrassDetectStatus::NO_GRASS};
    // 障碍物检测结果
    ObstacleResult obstacle_result;
    // 边界检测结果
    BoundaryResult boundary_result;
    // 机器人中心坐标（在图像中的像素坐标）
    Point mower_point;
    // 距离机器人最近的障碍物点
    Point min_dist_point;
    // 输入算法的图像时间戳
    uint64_t input_timestamp;
    // 算法执行完成时的时间戳
    uint64_t output_timestamp;
    // 占据栅格
    OccupancyResult occupancy_grid;
};

enum class ChargeStationDirection : int
{
    LEFT = -1, // 左边
    FRONT = 0, // 居中
    RIGHT = 1  // 右边
};

enum class ChargeStationPose : int
{
    LEFT = -1, // 左边
    FRONT = 0, // 居中
    RIGHT = 1  // 右边
};

// 充电桩检测结果
struct ChargeStationDetectResult
{
    uint64_t timestamp_ms{0};
    // 是否检测到充电桩
    bool is_chargestation{false};
    // 是否检测到充电桩头部.
    bool is_head{false};
    // 充电桩相对相机的偏移方向(小车的旋转方向)，左中右分别为-1， 0， 1
    ChargeStationDirection direction{ChargeStationDirection::FRONT};
    // 充电桩的头部朝向，左:-1、中:0、右:1.
    ChargeStationPose pose{ChargeStationPose::FRONT};
    // 目标的距离远近程度，值越小距离越近，目前有4档(0 1 2 3).
    int range{0};
    // 充电桩整体检测框的类别id、置信度和左上右下角的像素坐标(classID, conf, x1, y1, x2, y2).
    std::vector<float> station_box;
    // 充电桩头部检测框的类别id、置信度和左上右下角的像素坐标(classID, conf, x1, y1, x2, y2).
    std::vector<float> head_box;
    // 充电桩整体像素坐标对相机中心的偏移方向.
    int charge_station_center_error;
    // 充电桩头部像素坐标对相机中心的偏移方向.
    int head_center_error;

    void Reset()
    {
        is_head = false;
        is_chargestation = false;
    }
};

enum class QRCodeDetectStatus : int
{
    NO_DETECT_QRCODE = 0,       // 未检测到二维码
    DETECT_QRCODE_NO_POSE = 1,  // 检测到二维码，不能算出位姿
    DETECT_QRCODE_HAVE_POSE = 2 // 可以计算出位姿
};

// 二维码检测结果
struct QRCodeLocationResult
{
    uint64_t timestamp_ms;                                                  // 时间戳
    int mark_perception_status;                                             // 0 表示未感知（检测）到二维码；1 表示感知（检测）到二维码
    int mark_perception_direction;                                          // -1 偏左，跨区信标在相机画面中偏左; 0 居中；1 偏右
    QRCodeDetectStatus detect_status{QRCodeDetectStatus::NO_DETECT_QRCODE}; // mark位姿检测状态
    std::vector<std::pair<int, float>> v_markID_dis;                        // 检测到的所有 markID 和 Mark 2 Camera 的距离 m（米）
    int markID;                                                             // 待（正在）检测的二维码 ID
    int target_direction;                                                   //-1 偏左，需左转向; 0 居中；1 偏右，需右转向
    float quaternion_wxyz[4];                                               // Robot 2 Target 旋转四元数表示
    VecXYZRPW xyzrpw;                                                       // 欧拉角
    void Reset()
    {
        detect_status = QRCodeDetectStatus::NO_DETECT_QRCODE;
    }
};

/**
 * @brief 轮式里程计当前小车线速度和角速度
 */
struct OdomResult
{
    float linear;
    float angular;
};

/**
 * @brief 检测到信标和其到camera的距离
 */
struct MarkIdDistance
{
    int mark_id;
    float distance;
};

/*
detect_status:
    0, 未检测到二维码
    1, 检测到二维码，不能算出位姿
    2  可以计算出位姿
mark_perception_status:
    0 表示未感知（检测）到信标；1 表示感知（检测）到信标
mark_perception_direction:
    -1 偏左，跨区信标在相机画面中偏左; 0 居中；1 偏右
roi_confidence:
    -1 表示未检测成功
    0 表示未在ROI区域
    1 表示在ROI区域

    检测到信标时的置信度：0~100，数值越到表示置信度越高，mark点越接近图像中心
target_direction:
    -1 表示未检测成功; 0 左转向；1 右转向
mark_id:
    正在检测的锥桶ID
mark_id_distance:
    检测到的所有 markID 和 Mark 2 Camera 的距离 m（米）
xyzrpw:
    X, Y, Z, ROLL, PITCH, YAW
*/
struct MarkLocationResult
{
    uint64_t timestamp; // ms
    int detect_status;
    int mark_perception_status;
    int mark_perception_direction;
    int roi_confidence;
    int target_direction;
    int mark_id;
    std::vector<MarkIdDistance> mark_id_distance;
    VecXYZRPW xyzrpw;
    void Reset()
    {
        detect_status = 0;
    }
};

// X轴加速度（m/s2），float 类型
// Y 轴加速度（m/s2），float 类型
// Z轴加速度（m/s2），float 类型
// X轴角速度（rad/s），float 类型
// Y轴角速度（rad/s），float 类型
// Z 轴角速度（rad/s），float 类型
struct ImuData
{
    uint64_t frame_timestamp; // 单位：毫秒，uint64_t 类型
    uint64_t system_timestamp;
    float linear_acceleration_x; //
    float linear_acceleration_y;
    float linear_acceleration_z;
    float angular_velocity_x;
    float angular_velocity_y;
    float angular_velocity_z;
};

// 左电机转速，需除以 99.5才能得到左电机实际转速，单位：RPM（转每分钟），float 类型
// 右电机转速，，需除以99.5才能得到左电机实际转速单位：RPM（转每分钟），float 类型
struct MotorSpeedData
{
    uint64_t frame_timestamp; // 单位：毫秒，uint64_t 类型
    uint64_t system_timestamp;
    float motor_speed_left;
    float motor_speed_right;
    float current_left;  // 左电机电流（安培）
    float current_right; // 右电机电流（安培）
};

struct MotionDetectionResult
{
    double ave_pix_diff;
    uint64_t timestamp; // unit: ms
    bool is_motion;
};

} // namespace fescue_iox

#endif
