#include "nav_common.hpp"

#include <cmath>

namespace fescue_iox
{

/**
 * @brief Get the Velocity From Motor R P M object
 *
 * @param left_rpm 左轮电机转速
 * @param right_rpm 右轮电机转速
 * @param wheel_radius 车轮半径
 * @param wheel_base 车轮轴距
 * @param linear_velocity 转换后的线速度
 * @param angular_velocity 转换后的角速度
 */
void GetVelocityFromMotorRPM(float left_rpm, float right_rpm, float wheel_radius, float wheel_base, float &linear_velocity, float &angular_velocity)
{
    // 将电机转速从RPM转换为rad/s
    float w_left = left_rpm * 2 * M_PI / 60.0f;
    float w_right = right_rpm * 2 * M_PI / 60.0f;

    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;

    linear_velocity = (v_right + v_left) / 2.0f;
    angular_velocity = (v_right - v_left) / wheel_base;
}

} // namespace fescue_iox
