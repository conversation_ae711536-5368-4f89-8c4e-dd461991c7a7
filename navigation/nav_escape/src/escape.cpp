#include "escape.hpp"

#include "escape_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <random>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationEscapeAlg::NavigationEscapeAlg(const EscapeAlgParam &param)
{
    SetAlgParam(param);
}

NavigationEscapeAlg::~NavigationEscapeAlg()
{
    LOG_WARN("NavigationEscapeAlg exit!");
}

EscapeAlgResult NavigationEscapeAlg::Run(const PerceptionFusionResult &fusion_result,
                                         const ImuData &imu_data,
                                         const MotorSpeedData &motor_speed_data)
{
    LOG_DEBUG_THROTTLE(1000, "updaeEncoder and updateImu");
    UpdateImu(imu_data);
    UpdateEncoder(motor_speed_data);
    // record(imu_data, motor_speed_data);
    // loop check
    if (path_long_ >= min_path_long_ && path_long_ < max_path_long_)
    {
        LoopCheck();
        if (is_loop_)
        {
            LOG_ERROR_THROTTLE(1000, "Play attention LOOP!!!!!!!!!!!!");
            is_loop_ = false;
            return EscapeAlgResult(true);
        }
    }
    if (path_long_ >= max_path_long_) // whethe should reduce !is_loop
    {
        Reset();
    }
    LOG_ERROR_THROTTLE(1000, "path_long_:{},theta:{}", path_long_, turn_right_theta_);
    return EscapeAlgResult(false);
}

void NavigationEscapeAlg::ResetEscapeFlags()
{
    linear_ = 0;
    angular_ = 0;
    path_long_ = 0;
    turn_right_theta_ = 0;
    cur_pose_.setZero();
    poses_.clear();
    imu_datas_.clear();
    encoder_datas_.clear();
    last_encoder_timestamp_ = 0;
    theta_last_ = 0;
    is_loop_ = false;
}

bool NavigationEscapeAlg::SetAlgParam(const EscapeAlgParam &param)
{
    // todo
    return true;
}

EscapeAlgParam NavigationEscapeAlg::GetAlgParam()
{
    return EscapeAlgParam();
}

void NavigationEscapeAlg::UpdateImu(ImuData imu)
{
    uint64_t time_stamp = imu.system_timestamp;
    float gx = imu.angular_velocity_x;
    float gy = imu.angular_velocity_y;
    float gz = imu.angular_velocity_z;
    if (!bias_complete_)
    {
        if (imu_datas_.size() < 150)
        {
            angular_ = 0;
        }
        else
        {
            Eigen::Vector3d bias = Eigen::Vector3d::Zero();
            for (int i = 0; i < imu_datas_.size(); i++)
            {
                bias[0] += imu_datas_[i].angular_velocity_x;
                bias[1] += imu_datas_[i].angular_velocity_y;
                bias[2] += imu_datas_[i].angular_velocity_z;
            }
            bias /= imu_datas_.size();
            bias_ = bias;
            angular_ = -gz + bias_[2];
            bias_complete_ = true;
        }
    }
    else
    {
        angular_ = -gz + bias_[2];
    }
    imu_datas_.push_back(imu);
}

void NavigationEscapeAlg::UpdateEncoder(MotorSpeedData encoder)
{
    // float cur_encoder_timestamp = float(encoder.timestamp) / 1000;
    uint64_t cur_encoder_timestamp = encoder.system_timestamp;
    // LOG_ERROR("cur_encoder_timestamp:{}", cur_encoder_timestamp);
    if (encoder_datas_.size() == 0)
    {
        last_encoder_timestamp_ = cur_encoder_timestamp;
    }
    else
    {
        float dt = float(cur_encoder_timestamp - last_encoder_timestamp_) / 1000;
        if (dt <= 0)
        {
            LOG_DEBUG_THROTTLE(1000, "encoder data timestamp error,dt <=0,cur:{},last:{}", cur_encoder_timestamp, last_encoder_timestamp_);
            // TODO:should report error
        }
        else
        {
            float v_left = (encoder.motor_speed_left * left_wheel_diameter_ / 2) / encoder_resolution_;
            float v_right = (encoder.motor_speed_right * right_wheel_diameter_ / 2) / encoder_resolution_;
            float wheel_angular = (v_right - v_left) / wheel_base_;
            linear_ = (v_left + v_right) / 2;
            if (imu_datas_.size() == 0)
            {
                angular_ = wheel_angular;
            }
            else
            {
                uint64_t cur_imu_timestamp = imu_datas_[imu_datas_.size() - 1].system_timestamp;
                float diff_imu_encoder_timestamp = (cur_imu_timestamp > cur_encoder_timestamp) ? float(cur_imu_timestamp - cur_encoder_timestamp) / 1000 : float(cur_encoder_timestamp - cur_imu_timestamp) / 1000;
                if (diff_imu_encoder_timestamp > 0.05)
                {
                    angular_ = wheel_angular;
                    LOG_DEBUG_THROTTLE(1000, "The difference between imu timestamp and encoder timestamp is too large,imu:{},encoder:{},diff:{}", cur_imu_timestamp, cur_encoder_timestamp, float(cur_imu_timestamp - cur_encoder_timestamp) / 1000);
                }
            }
            float delta_theta = angular_ * dt;
            turn_right_theta_ += delta_theta;
            float theta_new = theta_last_ + delta_theta;
            theta_new = NormalizeAngle(theta_new);
            float x_new = cur_pose_(0) + linear_ * cos(theta_new) * dt;
            float y_new = cur_pose_(1) + linear_ * sin(theta_new) * dt;
            path_long_ += std::hypot(x_new - cur_pose_(0), y_new - cur_pose_(1));
            cur_pose_(0) = x_new;
            cur_pose_(1) = y_new;
            theta_last_ = theta_new;
            poses_.push_back(cur_pose_);
            if (is_save_data_)
            {
                std::ofstream datafile(file_path_ + "pose.txt", std::ofstream::app);
                if (!datafile.is_open())
                {
                    LOG_ERROR_THROTTLE(1000, "can not open file!!!!");
                }
                else
                {
                    int datas_num = poses_.size();
                    datafile << std::fixed << datas_num << ", " << cur_encoder_timestamp << ", " << cur_pose_(0) << ", " << cur_pose_(1) << ", " << theta_new << ", " << angular_ << ", " << path_long_ << ", " << turn_right_theta_ << std::endl;
                }
                datafile.close();
            }
        }
        last_encoder_timestamp_ = cur_encoder_timestamp;
    }
    encoder_datas_.push_back(encoder);
}

#if 0
void NavigationEscapeAlg::LoopCheck()
{
    int loop_num = 0;
    for (int i = poses_.size() - 1; i > poses_.size() - back_check_num_; i -= 10)
    {
        for (int j = 0; j < i; j++)
        {
            float distance = std::hypot(poses_.at(i)(0) - poses_.at(j)(0), poses_.at(i)(1) - poses_.at(j)(1));
            if (distance < pose_error_ && i - j > pose_interval_num_)
            {
                loop_num++;
            }
        }
    }
    if ((loop_num > 0 && turn_right_theta_ < -6)) // -12.5//work:-6
    {
        is_loop_ = true;
        LOG_ERROR_THROTTLE(1000, "!!Loop_num > 0 and turn_right_theta_ < -6!!");
    }
    else if (turn_right_theta_ < -50)
    {
        is_loop_ = true;
        LOG_ERROR_THROTTLE(1000, "!!turn_right_theta_ < -50!!");
    }
    else
    {
        is_loop_ = false;
    }
    LOG_ERROR_THROTTLE(1000, "turn right theta:{} ,loop_num:{},is_loop_:{}", turn_right_theta_, loop_num, is_loop_);
}
#endif

void NavigationEscapeAlg::LoopCheck()
{
    if ((turn_right_theta_ < turn_right_threhold_)) // -12.5//work:-6
    {
        is_loop_ = true;
        LOG_ERROR_THROTTLE(1000, "!!Loop_num > 0 and turn_right_theta_ < -6!!");
    }
    else
    {
        is_loop_ = false;
    }
    LOG_ERROR_THROTTLE(1000, "turn right theta:{} ,is_loop_:{}", turn_right_theta_, is_loop_);
}

float NavigationEscapeAlg::NormalizeAngle(float angle)
{
    return atan2(sin(angle), cos(angle)); // 归一化到 [-π, π]
}

void NavigationEscapeAlg::Reset()
{
    linear_ = 0;
    angular_ = 0;
    path_long_ = 0;
    turn_right_theta_ = 0;
    cur_pose_.setZero();
    poses_.clear();
    imu_datas_.clear();
    encoder_datas_.clear();
    last_encoder_timestamp_ = 0;
    theta_last_ = 0;
    is_loop_ = false;
}

void NavigationEscapeAlg::record(ImuData imu, MotorSpeedData encoder)
{
    std::ofstream imu_datafile(file_path_ + "imu.txt", std::ofstream::app);
    if (!imu_datafile.is_open())
    {
        LOG_ERROR_THROTTLE(1000, "can not open imu_datafile!!!!");
    }
    else
    {
        imu_datafile << std::fixed << save_data_num_ << ", " << imu.system_timestamp << ", " << imu.angular_velocity_x << ", " << imu.angular_velocity_y << ", " << imu.angular_velocity_z << ", " << imu.linear_acceleration_y << ", " << imu.linear_acceleration_z << std::endl;
    }
    imu_datafile.close();

    std::ofstream encoder_datafile(file_path_ + "encoder.txt", std::ofstream::app);
    if (!encoder_datafile.is_open())
    {
        LOG_ERROR_THROTTLE(1000, "can not open encoder_datafile!!!!");
    }
    else
    {
        encoder_datafile << std::fixed << save_data_num_ << ", " << encoder.system_timestamp << ", " << encoder.motor_speed_left << ", " << encoder.motor_speed_right << std::endl;
    }
    encoder_datafile.close();
    save_data_num_++;
}

} // namespace fescue_iox