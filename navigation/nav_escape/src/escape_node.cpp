#include "escape_node.hpp"

#include "mower_sdk_version.h"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationEscapeNode::NavigationEscapeNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitAlgorithm();
    InitHeartbeat();
}

NavigationEscapeNode::~NavigationEscapeNode()
{
    DeinitAlgorithm();
    LOG_WARN("NavigationEscapeNode exit!");
}

void NavigationEscapeNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationEscapeNode::InitParam()
{
    const std::string conf_file{"conf/navigation_escape_node/navigation_escape_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationEscapeNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationEscapeNode create config path failed!!!");
        }
    }
    if (!Config<NavigationEscapeNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationEscapeNode config parameters failed!");
    }
    NavigationEscapeNodeConfig config = Config<NavigationEscapeNodeConfig>::GetConfig();
    LOG_INFO("[navigation_escape_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_escape_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_escape_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());
    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    escape_alg_conf_file_ = config.escape_alg_conf_file;
    Config<NavigationEscapeNodeConfig>::SetConfig(config, true);
    CreateDirectories(log_dir_);
}

void NavigationEscapeNode::InitAlgorithmParam()
{
    std::string conf_path = GetDirectoryPath(escape_alg_conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Navigation escape algo create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Navigation escape algo create config path failed!!!");
        }
    }
    if (!Config<NavigationEscapeAlgConfig>::Init(escape_alg_conf_file_))
    {
        LOG_WARN("Init Navigation escape algo config parameters failed!");
    }
    NavigationEscapeAlgConfig config = Config<NavigationEscapeAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    Config<NavigationEscapeAlgConfig>::SetConfig(config, true);
}

void NavigationEscapeNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationEscapeNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationEscapeNode::InitPublisher()
{
    pub_escape_result_ = std::make_unique<IceoryxPublisherMower<ob_mower_msgs::NavEscapeResult>>("navigation_escape_result");
}

void NavigationEscapeNode::InitSubscriber()
{
    sub_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 1, [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            DealFusionResult(data);
        });

    sub_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_nav_alg_ctrl", 1, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            DealNavAlgCtrlResult(data);
        });

    sub_nav_running_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>>(
        "navigation_running_state", 1, [this](const fescue_msgs__msg__NavigationRunningStateData &data, const std::string &event) {
            DealNavRunningState(data);
        });

    sub_mcu_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuImu>>(
        "mcu_imu", 1, [this](const mower_msgs::msg::McuImu &data, const std::string &event) {
            DealMcuImu(data);
        });

    sub_mcu_motor_speed_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>>(
        "mcu_motor_speed", 1, [this](const mower_msgs::msg::McuMotorSpeed &data, const std::string &event) {
            DealMcuMotorSpeed(data);
        });
}

void NavigationEscapeNode::InitAlgorithm()
{
    InitAlgorithmParam();
    escape_alg_ = std::make_unique<NavigationEscapeAlg>(escape_alg_param_);
    thread_running_.store(true);
    escape_thread_ = std::thread(&NavigationEscapeNode::EscapeThread, this);
}

void NavigationEscapeNode::DeinitAlgorithm()
{
    thread_running_.store(false);
    if (escape_thread_.joinable())
    {
        escape_thread_.join();
    }
}

void NavigationEscapeNode::InitService()
{
    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_navigation_escape_node_param_request", 10U,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = DealSetNodeParam(request.data);
            LOG_INFO("Set navigation escape node param execute {}", response.success);
        });
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_navigation_escape_node_param_request", 10U,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            response.success = DealGetNodeParam(response.data);
            LOG_INFO("Get navigation escape node param execute {}", response.success);
        });
    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_navigation_escape_alg_param_request", 10U,
        [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = DealSetAlgParam(request.data);
            LOG_INFO("Set navigation escape node param execute {}", response.success);
        });
    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_navigation_escape_alg_param_request", 10U,
        [this](const get_alg_param_request &request, get_alg_param_response &response) {
            response.success = DealGetAlgParam(response.data);
            LOG_INFO("Get navigation escape node param execute {}", response.success);
        });
}

void NavigationEscapeNode::DealFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg)
{
    std::lock_guard<std::mutex> lock(fusion_mtx_);
    GetFusionGrassDetectStatus(msg, fusion_result_.grass_detecte_status);
    GetFusionObstacleResult(msg, fusion_result_.obstacle_result);
    GetFusionBoundaryResult(msg, fusion_result_.boundary_result);
    fusion_result_.mower_point.x = msg.mower_point.x;
    fusion_result_.mower_point.y = msg.mower_point.y;
    fusion_result_.min_dist_point.x = msg.min_dist_point.x;
    fusion_result_.min_dist_point.y = msg.min_dist_point.y;
    fusion_result_.input_timestamp = msg.timestamp;
    fusion_result_.output_timestamp = msg.output_timestamp;
}

void NavigationEscapeNode::DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg)
{
    for (size_t i = 0; i < msg.data.size(); i++)
    {
        if (msg.data[i].type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_ESCAPE)
        {
            switch (msg.data[i].state)
            {
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE:
                escape_enable_.store(false);
                break;
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE:
                escape_enable_.store(true);
                break;
            default:
                break;
            }
            break;
        }
    }
}

void NavigationEscapeNode::DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data)
{
    // if (escape_alg_)
    // {
    //     escape_alg_->SetAlgoRunningState(static_cast<MowerRunningState>(data.state));
    // }
}

void NavigationEscapeNode::DealMcuImu(const mower_msgs::msg::McuImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);
    imu_data_.angular_velocity_x = data.angular_velocity_x;
    imu_data_.angular_velocity_y = data.angular_velocity_y;
    imu_data_.angular_velocity_z = data.angular_velocity_z;
    imu_data_.linear_acceleration_x = data.linear_acceleration_x;
    imu_data_.linear_acceleration_y = data.linear_acceleration_y;
    imu_data_.linear_acceleration_z = data.linear_acceleration_z;
    imu_data_.frame_timestamp = data.frame_timestamp;
    imu_data_.system_timestamp = data.system_timestamp;
    // update IMU datas
}

void NavigationEscapeNode::DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_.motor_speed_left = data.motor_speed_left;
    motor_speed_data_.motor_speed_right = data.motor_speed_right;
    motor_speed_data_.frame_timestamp = data.frame_timestamp;
    motor_speed_data_.system_timestamp = data.system_timestamp;
    // update encoder datas
}

bool NavigationEscapeNode::DealSetNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    NavigationEscapeNodeConfig config = Config<NavigationEscapeNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<NavigationEscapeNodeConfig>::SetConfig(config, true);
    LOG_INFO("New NavigationEscapeNode params: {}", config.toString().c_str());
    return true;
}

bool NavigationEscapeNode::DealGetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    NavigationEscapeNodeConfig config = Config<NavigationEscapeNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool NavigationEscapeNode::DealSetAlgParam(const ob_mower_srvs::NavEscapeAlgParam &data)
{
    if (!escape_alg_)
        return false;
    NavigationEscapeAlgConfig config = Config<NavigationEscapeAlgConfig>::GetConfig();
    LOG_INFO("New NavigationEscapeAlgConfig params: {}", config.toString().c_str());
    Config<NavigationEscapeAlgConfig>::SetConfig(config, true);
    EscapeAlgParam param;
    // todo
    return escape_alg_->SetAlgParam(param);
}

bool NavigationEscapeNode::DealGetAlgParam(ob_mower_srvs::NavEscapeAlgParam &data)
{
    if (!escape_alg_)
        return false;
    EscapeAlgParam param = escape_alg_->GetAlgParam();
    // todo
    return true;
}

void NavigationEscapeNode::EscapeThread()
{
    PerceptionFusionResult fusion_result;
    ImuData imu_data;
    MotorSpeedData motor_speed_data;

    while (thread_running_.load())
    {
        if (escape_enable_.load())
        {
            {
                std::scoped_lock lock(fusion_mtx_, imu_mtx_, motor_speed_mtx_);
                fusion_result = fusion_result_;
                imu_data = imu_data_;
                motor_speed_data = motor_speed_data_;
            }
            if (escape_alg_)
            {
                auto result = escape_alg_->Run(fusion_result, imu_data, motor_speed_data);
                LOG_ERROR_THROTTLE(1000, "escape node result: {}", result.loop);
                PublishResult(result.loop);
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationEscapeAlg is disable!");
            PublishResult(false);
            if (escape_alg_)
            {
                escape_alg_->ResetEscapeFlags();
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
}

void NavigationEscapeNode::PublishResult(bool is_loop)
{
    if (pub_escape_result_)
    {
        ob_mower_msgs::NavEscapeResult escape_result;
        escape_result.timestamp_ms = GetTimestampMs();
        escape_result.is_loop = is_loop;
        pub_escape_result_->publishCopyOf(escape_result);
    }
}

} // namespace fescue_iox
