#pragma once

#include "data_type.hpp"
#include "thirdparty/math/eigen3/Eigen/Dense"
#include "velocity_publisher.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <string>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct EscapeAlgParam
{
    EscapeAlgParam() = default;
};

struct EscapeAlgResult
{
    bool loop{false};
    EscapeAlgResult(bool loop = false)
        : loop(loop)

    {
    }
};

class NavigationEscapeAlg
{
public:
    NavigationEscapeAlg(const EscapeAlgParam &param);
    ~NavigationEscapeAlg();
    EscapeAlgResult Run(const PerceptionFusionResult &fusion_result, const ImuData &imu_data, const MotorSpeedData &motor_speed_data);
    void ResetEscapeFlags();
    bool SetAlgParam(const EscapeAlgParam &param);
    EscapeAlgParam GetAlgParam();

private:
    void UpdateImu(ImuData imu);
    void UpdateEncoder(MotorSpeedData encoder);
    void LoopCheck();
    float NormalizeAngle(float angle);
    void Reset();
    void record(ImuData imu, MotorSpeedData encoder);

private:
    // 状态变量
    float duration_{60000}; // How often to update the detection starting point (ms)
    float linear_{0};
    float angular_{0};
    float left_wheel_diameter_{0.2};
    float right_wheel_diameter_{0.2};
    float encoder_resolution_{9.549};
    float wheel_base_{0.35};
    float path_long_{0};
    float min_path_long_{5.0};  // 5
    float max_path_long_{50.0}; // 10,//30 support three type island
    Eigen::Vector2d cur_pose_{0, 0};
    Eigen::Vector3d bias_;
    std::vector<Eigen::Vector2d> poses_;
    std::vector<ImuData> imu_datas_;
    float last_time_imu_{0};
    std::vector<MotorSpeedData> encoder_datas_;
    // float last_encoder_timestamp_{0};
    uint64_t last_encoder_timestamp_{0};
    bool bias_complete_{false};
    float theta_last_{0};
    std::string file_path_{"/userdata/"};
    bool is_save_data_{false};
    bool is_loop_{false};
    int back_check_num_{1000};
    float turn_right_theta_{0};
    int save_data_num_{1};
    int pose_interval_num_{2000};
    float pose_error_{0.1};
    float turn_right_threhold_{-9.4}; // 2pi*1.5
};

} // namespace fescue_iox
