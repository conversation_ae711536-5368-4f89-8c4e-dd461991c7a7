#include "behavior.hpp"

#include "behavior_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationBehaviorAlg::NavigationBehaviorAlg(const BehaviorAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("Behavior"))
{
    SetBehaviorAlgParam(param);

    InitSlipDetection();
}

NavigationBehaviorAlg::~NavigationBehaviorAlg()
{
    PublishVelocity(0.0, 0.0, 1000); // 持续发送1s

    DeinitSlipDetection();
    LOG_WARN("NavigationBehaviorAlg exit!");
}

void NavigationBehaviorAlg::InitSlipDetection()
{
    slip_detection_running_.store(true);
    slip_detection_thread_ = std::thread(&NavigationBehaviorAlg::SlipDetectionThread, this);
}

void NavigationBehaviorAlg::DeinitSlipDetection()
{
    slip_detection_running_.store(false);
    if (slip_detection_thread_.joinable())
    {
        slip_detection_thread_.join();
    }
}

void NavigationBehaviorAlg::SlipDetectionThread()
{
    while (slip_detection_running_.load())
    {
        // 获取最新的电机速度和运动检测数据
        MotorSpeedData motor_speed_data;
        MotionDetectionResult motion_detection_result;

        {
            std::lock_guard<std::mutex> lock(motor_speed_mtx_);
            motor_speed_data = motor_speed_data_;
        }

        {
            std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
            motion_detection_result = motion_detection_result_;
        }

        // 执行打滑检测
        bool current_slip = IsWheelSlipping(motor_speed_data, motion_detection_result, wheel_radius_, wheel_base_);
        // LOG_ERROR("[MowerAlg] [SlipDetectionThread] current_slip({})", current_slip);

        // 更新打滑状态
        is_slipping_detected_.store(current_slip);

        // 控制检测频率
        // std::this_thread::sleep_for(std::chrono::milliseconds(1000 / slip_detection_frequency_));
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
}

void NavigationBehaviorAlg::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_ = motor_speed_data;
}

void NavigationBehaviorAlg::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_ = motion_detection_result;
}

void NavigationBehaviorAlg::SetBehaviorAlgParam(const BehaviorAlgParam &param)
{
    /****************************************碰撞恢复****************************************************/
    collision_backup_speed_ = param.collision_backup_speed;       // 后退速度 (m/s)/*param*/
    collision_backup_duration_ = param.collision_backup_duration; // 后退持续时间 (ms)/*param*/
    collision_turn_angle_ = param.collision_turn_angle;           // 转向角度 (45度)/*param*/
    collision_turn_speed_ = param.collision_turn_speed;           // 转向速度 (rad/s)/*param*/

    /****************************************抬升恢复****************************************************/
    lift_backup_speed_ = param.lift_backup_speed;       // 恢复阶段后退速度 m/s/*param*/
    lift_backup_duration_ = param.lift_backup_duration; // 恢复阶段后退持续时间 ms/*param*/

    /****************************************脱困恢复****************************************************/
    backup_speed_ = param.backup_speed;         // 后退速度 (m/s)/*param*/
    turn_angle_ = param.turn_angle;             // 旋转角度 (45度)/*param*/
    forward_speed_ = param.forward_speed;       // 前进速度 (m/s)/*param*/
    backup_duration_ = param.backup_duration;   // 后退持续时间 (ms)/*param*/
    turn_duration_ = param.turn_duration;       // 旋转持续时间 (ms)/*param*/
    forward_duration_ = param.forward_duration; // 前进持续时间 (ms)/*param*/
}

void NavigationBehaviorAlg::GetBehaviorAlgParam(BehaviorAlgParam &param)
{
    /****************************************碰撞恢复****************************************************/
    param.collision_backup_speed = collision_backup_speed_;       // 后退速度 (m/s)/*param*/
    param.collision_backup_duration = collision_backup_duration_; // 后退持续时间 (ms)/*param*/
    param.collision_turn_angle = collision_turn_angle_;           // 转向角度 (45度)/*param*/
    param.collision_turn_speed = collision_turn_speed_;           // 转向速度 (rad/s)/*param*/

    /****************************************抬升恢复****************************************************/
    param.lift_backup_speed = lift_backup_speed_;       // 恢复阶段后退速度 m/s/*param*/
    param.lift_backup_duration = lift_backup_duration_; // 恢复阶段后退持续时间 ms/*param*/

    /****************************************脱困恢复****************************************************/
    param.backup_speed = backup_speed_;         // 后退速度 (m/s)/*param*/
    param.turn_angle = turn_angle_;             // 旋转角度 (45度)/*param*/
    param.forward_speed = forward_speed_;       // 前进速度 (m/s)/*param*/
    param.backup_duration = backup_duration_;   // 后退持续时间 (ms)/*param*/
    param.turn_duration = turn_duration_;       // 旋转持续时间 (ms)/*param*/
    param.forward_duration = forward_duration_; // 前进持续时间 (ms)/*param*/
}

void NavigationBehaviorAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationBehaviorAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_DEBUG("[NavigationBehaviorAlg] Unknown state {}!", static_cast<int>(state));
    }
}

void NavigationBehaviorAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationBehaviorAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationBehaviorAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0, 50);
}

void NavigationBehaviorAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationBehaviorAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationBehaviorAlg::ShowMowerRunningInfo()
{
    // 打印时间差
    // LOG_WARN("[Behavior] 沿边感知驱动冷却的计时（秒）：({})!", perception_drive_duration_.count());
}

void NavigationBehaviorAlg::ResetBehaviorFlags()
{
    behavior_status_ = BehaviorRunningState::UNDEFINED;

    /****************************************碰撞恢复****************************************************/
    collision_state_ = CollisionRecoveryState::NORMAL;
    collision_recovery_completed_ = false;
    collision_recovery_succeed_ = false;

    /****************************************抬升恢复****************************************************/
    lift_state_ = LiftState::NORMAL;
    lift_completed_ = false;
    lift_succeed_ = false;

    /****************************************脱困恢复****************************************************/
    unstuck_state_ = UnstuckState::NORMAL;
    unstuck_completed_ = false;
    unstuck_succeed_ = false;
    unstuck_attempts_ = 0; // 当前尝试次数

    /****************************************打滑和堵转检测和恢复****************************************************/
    wheel_slip_state_ = WheelSlipRecoveryState::NORMAL;
    slipping_recovery_completed_ = false;
    slipping_recovery_succeed_ = false;
}

void NavigationBehaviorAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationBehaviorAlg::SetBehaviorRunningStateCallback(std::function<void(BehaviorRunningState)> callback)
{
    behavior_running_state_callback_ = callback;
}

const char *NavigationBehaviorAlg::GetVersion()
{
    return "V1.1.0";
}

void NavigationBehaviorAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationBehaviorAlg::UpdateBehaviorRunningState(BehaviorRunningState state)
{
    if (behavior_running_state_callback_)
    {
        behavior_running_state_callback_(state);
    }
}

/**
 * @brief 旋转运动控制
 *
 * @param yaw_des
 * @param yaw_first
 */
void NavigationBehaviorAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // 默认旋转方向 1.0左转 -1.0右转
    if (sign > 0)
    {
        LOG_DEBUG("[Behavior] 旋转方向：左转");
    }
    else
    {
        LOG_DEBUG("[Behavior] 旋转方向：右转");
    }

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // 转弯持续时间 ms
    LOG_DEBUG("[Behavior] 旋转角度 = {}", Radians2Degrees(ang_err));
    LOG_DEBUG("[Behavior] 角速度 = {}, 时间 = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

/**
 * @brief 直线运动控制
 *
 * @param pass_point
 * @param location
 */
void NavigationBehaviorAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                                const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_DEBUG("[Behavior] 直行距离 dis = {}", dis);
    LOG_DEBUG("[Behavior] 直行速度 = {}, 时间 = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

bool NavigationBehaviorAlg::IsStuck(/*const SensorData &sensor_data*/)
{
    // // 如果轮速大于阈值但实际移动速度小于阈值，则认为卡住
    // return sensor_data.wheel_speed > wheel_speed_threshold_ &&
    //        sensor_data.actual_speed < actual_speed_threshold_;

    is_stucked_ = false;
    return false;
}

void NavigationBehaviorAlg::SetCollisionStatus(const McuExceptionStatus &mcu_exception_status)
{
    if (mcu_exception_status == McuExceptionStatus::COLLISION)
    {
        LOG_DEBUG("[SetCollisionStatus] 检测碰撞");
        is_collision_detected_ = true;
    }
    else
    {
        LOG_DEBUG("[SetCollisionStatus] 未检测碰撞");
        is_collision_detected_ = false;
    }
}

void NavigationBehaviorAlg::SetLiftedStatus(const McuExceptionStatus &mcu_exception_status)
{
    if (mcu_exception_status == McuExceptionStatus::LIFTING)
    {
        LOG_DEBUG("[SetLiftedStatus] 检测抬升");
        is_lifted_ = true;
    }
    else
    {
        LOG_DEBUG("[SetLiftedStatus] 未检测抬升");
        is_lifted_ = false;
    }
}

void NavigationBehaviorAlg::ShowBehaviorPrint(BehaviorRunningState &behavior_state)
{
    std::string state_str;
    switch (behavior_state)
    {
    case BehaviorRunningState::RUNNING:
        state_str = "RUNNING";
        break;
    case BehaviorRunningState::SUCCESS:
        state_str = "SUCCESS";
        break;
    case BehaviorRunningState::FAILURE:
        state_str = "FAILURE";
        break;
    default:
        state_str = "UNDEFINED";
    }
    LOG_INFO("[DoBehavior] behavior_state：({}) {}", static_cast<int>(behavior_state), state_str);
}

BehaviorAlgResult NavigationBehaviorAlg::DoBehavior(PerceptionFusionResult &fusion_result,
                                                    McuExceptionStatus &mcu_exception_status,
                                                    const ImuData &imu_data,
                                                    const MotorSpeedData &motor_speed_data,
                                                    const MotionDetectionResult &motion_detection_result)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "[Behavior] DoBehavior() is PAUSE!");
        return BehaviorAlgResult(false, BehaviorStatus::InProgress);
    }

    // 处理抬升和碰撞数据
    SetCollisionStatus(mcu_exception_status);
    SetLiftedStatus(mcu_exception_status);

    // 打滑和堵转处理
    // bool is_stalling = IsMotorStalling(motor_speed_data);
    // bool is_slipping = IsWheelSlipping(motor_speed_data, imu_data, wheel_radius_, wheel_base_);

    // if (is_stalling)
    // {
    //     HandleMotorStall();
    //     return BehaviorAlgResult(true, BehaviorStatus::InProgress);
    // }

    // 打印
    ShowBehaviorPrint(behavior_status_);

    // 湿滑打滑处理
    HandleWheelSlip(fusion_result);
    if (slipping_recovery_completed_)
    {
        if (slipping_recovery_succeed_)
        {
            {
                behavior_status_ = BehaviorRunningState::SUCCESS;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Successed);
        }
        else
        {
            {
                behavior_status_ = BehaviorRunningState::FAILURE;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Failed);
        }
    }

    // 处理抬升恢复
    HandleLift();
    if (lift_completed_)
    {
        if (lift_succeed_)
        {
            {
                behavior_status_ = BehaviorRunningState::SUCCESS;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Successed);
        }
        else
        {
            {
                behavior_status_ = BehaviorRunningState::FAILURE;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Failed);
        }
    }

    // // 处理脱困和打滑
    // PerformUnstuck();
    // if (unstuck_completed_)
    // {
    //     if (unstuck_succeed_)
    //     {
    //         return BehaviorAlgResult(true, BehaviorStatus::Successed);
    //     }
    //     else
    //     {
    //         return BehaviorAlgResult(true, BehaviorStatus::Failed);
    //     }
    // }

    // 执行碰撞恢复
    PerformCollisionRecovery(fusion_result);
    if (collision_recovery_completed_)
    {
        if (collision_recovery_succeed_)
        {
            {
                behavior_status_ = BehaviorRunningState::SUCCESS;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Successed);
        }
        else
        {
            {
                behavior_status_ = BehaviorRunningState::FAILURE;
                UpdateBehaviorRunningState(behavior_status_);

                ShowBehaviorPrint(behavior_status_);
            }
            return BehaviorAlgResult(true, BehaviorStatus::Failed);
        }
    }

    return BehaviorAlgResult(false, BehaviorStatus::InProgress);
}

bool NavigationBehaviorAlg::IsWheelSlipping(const MotorSpeedData &motor_data,
                                            const ImuData &imu_data,
                                            float wheel_radius, float wheel_base)
{
    // 将电机转速从RPM转换为rad/s
    float w_left = motor_data.motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_data.motor_speed_right * 2 * M_PI / 60.0f;
    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;
    float theoretical_linear = (v_right + v_left) / 2.0f;
    float theoretical_angular = (v_right - v_left) / wheel_base;

    // 积分IMU加速度估算速度（简化形式，假设周期性更新）
    UpdatePreintegration(imu_data);
    float actual_linear = std::hypot(preint_state_.velocity_x, preint_state_.velocity_y);

    // 避免除零和无效情况
    if ((theoretical_linear < min_valid_linear_) && (theoretical_angular < min_valid_angular_))
    {
        return false;
    }

    float slip_ratio = std::abs(theoretical_linear - actual_linear) / theoretical_linear;
    // float slip_ratio = std::abs(theoretical_angular - actual_angular) / theoretical_angular;
    LOG_DEBUG("[Slip] 理论速度: {:.3f}, 实际速度: {:.3f}, 打滑率: {:.3f}",
              theoretical_linear, actual_linear, slip_ratio);
    return slip_ratio > slip_ratio_threshold_;
}

bool NavigationBehaviorAlg::IsWheelSlipping(const MotorSpeedData &motor_data,
                                            const MotionDetectionResult &motion_detection_result,
                                            float wheel_radius, float wheel_base)
{
    static bool is_slipping = false; // 跟踪当前是否打滑
    static int slip_counter = 0;
    const int slip_threshold = 200;

    // 将电机转速从RPM转换为rad/s
    float w_left = motor_data.motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_data.motor_speed_right * 2 * M_PI / 60.0f;
    // LOG_ERROR("[MowerAlg] [IsWheelSlipping1] w_left({}), w_right({})", w_left, w_right);

    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;

    float act_linear = (v_right + v_left) / 2.0f;
    float act_angular = (v_right - v_left) / wheel_base;

    // 时间同步检查
    // LOG_INFO("[MowerAlg] [IsWheelSlipping1] motor_data.timestamp({}), motion_detection_result.timestamp({})",
    //          motor_data.system_timestamp, motion_detection_result.timestamp);
    // LOG_INFO("[MowerAlg] [IsWheelSlipping1] 时间戳差值 ({})",
    //          std::abs(static_cast<int64_t>(motor_data.system_timestamp - motion_detection_result.timestamp)));

    // bool is_data_synced =
    //     std::abs(static_cast<int64_t>(motor_data.system_timestamp - motion_detection_result.timestamp)) < 1000; // ms

    // if (!is_data_synced)
    // {
    //     LOG_ERROR("[MowerAlg] [IsWheelSlipping1] 时间同步检查失败");
    //     return false;
    // }

    bool is_has_speed = (fabs(act_linear) > min_valid_linear_) || (fabs(act_angular) > min_valid_angular_);
    bool potential_slip = is_has_speed && !motion_detection_result.is_motion;

    if (!is_slipping)
    {
        if (potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = true;
            slip_counter = 0;
            // LOG_INFO("[MowerAlg] [IsWheelSlipping1] 检测到打滑状态");
        }
    }
    else // is_slipping == true
    {
        if (!potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = false;
            slip_counter = 0;
            // LOG_INFO("[MowerAlg] [IsWheelSlipping1] 打滑状态结束");
        }
    }

    // LOG_INFO("[nav_behavior] [IsWheelSlipping1] act_linear({}), act_angular({}), is_motion({}), is_slipping({}), slip_counter({})",
    //          act_linear, act_angular, motion_detection_result.is_motion, is_slipping, slip_counter);

    return is_slipping;
}

void NavigationBehaviorAlg::UpdatePreintegration(const ImuData &imu_data)
{
    float dt = (imu_data.system_timestamp - preint_state_.last_timestamp) / 1000.0f; // ms to s
    if (dt <= 0 || dt > 0.1f)
        dt = 0.01f; // 限制时间步长

    // 简单预积分：仅更新速度（忽略旋转和偏置）
    preint_state_.velocity_x += imu_data.linear_acceleration_x * dt;
    preint_state_.velocity_y += imu_data.linear_acceleration_y * dt;
    preint_state_.last_timestamp = imu_data.system_timestamp;
}

bool NavigationBehaviorAlg::IsMotorStalling(const MotorSpeedData &motor_data)
{
    float left_speed_mps = (motor_data.motor_speed_left / 99.5) * 2 * M_PI / 60.0f * wheel_radius_;
    float right_speed_mps = (motor_data.motor_speed_right / 99.5) * 2 * M_PI / 60.0f * wheel_radius_;
    float min_speed = std::min(std::abs(left_speed_mps), std::abs(right_speed_mps));
    float max_current = std::max(motor_data.current_left, motor_data.current_right); // 电流

    bool stalled = (max_current > stall_current_threshold_) &&
                   (min_speed < stall_speed_threshold_);
    LOG_DEBUG("[Stall] 电流: {:.2f} A, 速度: {:.3f} m/s, 是否堵转: {}",
              max_current, min_speed, stalled);
    return stalled;
}

void NavigationBehaviorAlg::HandleMotorStall()
{
    switch (unstuck_state_)
    {
    case UnstuckState::NORMAL:
    {
        LOG_WARN("[Stall] 检测到，开始后退");
        PublishVelocity(-backup_speed_, 0.0, 2000);
        unstuck_attempts_++;
        unstuck_state_ = UnstuckState::BACKING_UP;
        break;
    }

    case UnstuckState::BACKING_UP:
    {
        if (vel_publisher_ && vel_publisher_->IsExecutionCompleted())
        {
            float turn_angle = (unstuck_attempts_ % 2) ? M_PI / 6 : -M_PI / 6; // 交替30°
            LOG_INFO("[Stall] 转向 {:.1f} 度", Radians2Degrees(turn_angle));
            PublishVelocity(0.0, turn_angle, 1000);
            unstuck_state_ = UnstuckState::TURNING;
        }
        break;
    }

    case UnstuckState::TURNING:
    {
        if (vel_publisher_ && vel_publisher_->IsExecutionCompleted())
        {
            LOG_INFO("[Stall] 尝试前进");
            PublishVelocity(forward_speed_, 0.0, 2000);
            unstuck_state_ = UnstuckState::FORWARDING;
        }
        break;
    }

    case UnstuckState::FORWARDING:
    {
        if (vel_publisher_ && vel_publisher_->IsExecutionCompleted())
        {
            if (unstuck_attempts_ >= max_stall_retries_)
            {
                LOG_ERROR("[Stall] 达到最大重试次数，停止");
                // ProhibitVelPublisher();
                unstuck_state_ = UnstuckState::NORMAL;
                unstuck_attempts_ = 0;
            }
            else
            {
                unstuck_state_ = UnstuckState::NORMAL; // 重置以进行下次检查
            }
        }
        break;
    }

    default:
    {
        break;
    }
    }
}

void NavigationBehaviorAlg::HandleWheelSlip(const PerceptionFusionResult &fusion_result)
{
    switch (wheel_slip_state_)
    {
    case WheelSlipRecoveryState::NORMAL:
    {
        if (is_slipping_detected_.load())
        {
            // 复位
            behavior_status_ = BehaviorRunningState::RUNNING;
            slipping_recovery_completed_ = false;
            slipping_recovery_succeed_ = false;

            LOG_INFO("[HandleWheelSlip1] 检测到打滑，开始后退恢复");
            wheel_slip_state_ = WheelSlipRecoveryState::BACKING_UP;

            // PublishZeroVelocity();           // 立即停止
            // PublishVelocity(0.0, 0.0, 100); // 持续发送100ms
        }
        break;
    }
    case WheelSlipRecoveryState::BACKING_UP:
    {
        LOG_INFO("[HandleWheelSlip1] 后退中");
        PublishVelocity(-slipping_backup_speed_, 0.0, slipping_backup_duration_);

        LOG_INFO("[HandleWheelSlip1] 开始转向恢复");
        wheel_slip_state_ = WheelSlipRecoveryState::TURNING;

        break;
    }
    case WheelSlipRecoveryState::TURNING:
    {
        LOG_INFO("[HandleWheelSlip1] 转向中");
        float turn_angle = GetTurnAngle(fusion_result);
        float turn_duration = std::abs(turn_angle) / slipping_turn_speed_ * 1000;
        PublishVelocity(0.0, turn_angle > 0 ? slipping_turn_speed_ : -slipping_turn_speed_, turn_duration);

        LOG_INFO("[HandleWheelSlip1] 转向完成后，开始恢复");
        wheel_slip_state_ = WheelSlipRecoveryState::RECOVERING;

        // PublishVelocity(0.0, 0.0, 100); // 持续发送100ms

        break;
    }
    case WheelSlipRecoveryState::RECOVERING:
    {
        LOG_INFO("[HandleWheelSlip1] 恢复任务");
        wheel_slip_state_ = WheelSlipRecoveryState::NORMAL; // 恢复正常任务

        slipping_recovery_completed_ = true;
        slipping_recovery_succeed_ = true;
        break;
    }
    }
}

/**
 * @brief 处理脱困或打滑情况
 *
 */
void NavigationBehaviorAlg::PerformUnstuck()
{
    switch (unstuck_state_)
    {
    case UnstuckState::NORMAL:
    {
        if (is_stucked_)
        {
            LOG_DEBUG("[Unstuck] 检测到卡住，开始脱困");
            unstuck_state_ = UnstuckState::BACKING_UP;
            unstuck_attempts_++;

            // PublishZeroVelocity();           // 立即停止
            PublishVelocity(0.0, 0.0, 100); // 持续发送100ms
        }
        break;
    }
    case UnstuckState::BACKING_UP:
    {
        LOG_DEBUG("[Unstuck] 脱困阶段：后退");
        PublishVelocity(-backup_speed_, 0.0, backup_duration_);

        unstuck_state_ = UnstuckState::TURNING;
        break;
    }
    case UnstuckState::TURNING:
    {
        LOG_DEBUG("[Unstuck] 脱困阶段：旋转");
        // 交替左右旋转
        float turn_angle = (unstuck_attempts_ % 2 == 0) ? turn_angle_ : -turn_angle_;
        PublishVelocity(0.0, turn_angle, turn_duration_);
        unstuck_state_ = UnstuckState::FORWARDING;
        break;
    }
    case UnstuckState::FORWARDING:
    {
        LOG_DEBUG("[Unstuck] 脱困阶段：前进");
        PublishVelocity(forward_speed_, 0.0, forward_duration_);
        if (!is_stucked_)
        {
            LOG_DEBUG("[Unstuck] 脱困成功，重置状态");
            unstuck_state_ = UnstuckState::NORMAL;
            unstuck_attempts_ = 0;

            unstuck_completed_ = true;
            unstuck_succeed_ = true;
        }
        else if (unstuck_attempts_ >= max_unstuck_attempts_)
        {
            LOG_WARN("[Unstuck] 达到最大尝试次数，脱困失败，停止尝试");
            unstuck_state_ = UnstuckState::NORMAL;
            unstuck_attempts_ = 0;
            // PublishZeroVelocity();          // 停止运动
            PublishVelocity(0.0, 0.0, 100); // 持续发送100ms

            unstuck_completed_ = true;
            unstuck_succeed_ = false; // 失败

            // TODO ：通知用户或切换其他策略
        }
        else
        {
            LOG_DEBUG("[Unstuck] 未脱困，进入下一轮尝试");
            unstuck_state_ = UnstuckState::BACKING_UP;
        }
        break;
    }
    }
}

/**
 * @brief 处理抬升情况
 *
 * @param sensor_data
 */
void NavigationBehaviorAlg::HandleLift(/*const SensorData &sensor_data*/)
{
    switch (lift_state_)
    {
    case LiftState::NORMAL:
    {
        if (is_lifted_)
        {
            behavior_status_ = BehaviorRunningState::RUNNING;
            lift_succeed_ = false;
            lift_completed_ = false;

            LOG_INFO("[Behavior] 检测到抬升，停止运动");
            lift_state_ = LiftState::LIFTED;

            // PublishZeroVelocity();           // 立即停止
            // PublishVelocity(0.0, 0.0, 100); // 持续发送100ms
        }
        break;
    }
    case LiftState::LIFTED:
    {
        LOG_INFO("[Behavior] 检测到抬升，开始后退恢复");
        PublishVelocity(-lift_backup_speed_, 0.0, lift_backup_duration_); // 添加后退恢复动作

        if (!is_lifted_)
        {
            LOG_INFO("[Behavior] 检测到放回地面，进入恢复阶段");
            lift_state_ = LiftState::RECOVERING;
        }
        break;
    }
    case LiftState::RECOVERING:
    {
        LOG_INFO("[Behavior] 恢复任务");
        lift_state_ = LiftState::NORMAL;

        lift_succeed_ = true;
        lift_completed_ = true;

        break;
    }
    }
}

/**
 * @brief 处理碰撞恢复
 *
 * @param fusion_result
 */
void NavigationBehaviorAlg::PerformCollisionRecovery(const PerceptionFusionResult &fusion_result)
{
    switch (collision_state_)
    {
    case CollisionRecoveryState::NORMAL:
    {
        if (is_collision_detected_)
        {
            behavior_status_ = BehaviorRunningState::SUCCESS;
            collision_recovery_completed_ = false;
            collision_recovery_succeed_ = false;

            LOG_INFO("[Collision] 检测到碰撞，开始后退恢复");
            collision_state_ = CollisionRecoveryState::BACKING_UP;

            // PublishZeroVelocity();           // 立即停止
            // PublishVelocity(0.0, 0.0, 100); // 持续发送100ms
        }
        break;
    }
    case CollisionRecoveryState::BACKING_UP:
    {
        LOG_INFO("[Collision] 后退中");
        PublishVelocity(-collision_backup_speed_, 0.0, collision_backup_duration_);

        if (is_collision_detected_)
        {
            LOG_INFO("[Collision] 检测到碰撞，开始转向恢复");
            collision_state_ = CollisionRecoveryState::TURNING;
        }
        else
        {
            LOG_INFO("[Collision] 后退后未检测到碰撞，开始恢复");
            collision_state_ = CollisionRecoveryState::RECOVERING;
        }

        // if (!is_collision_detected_)
        // {
        //     LOG_INFO("[Collision] 后退后未检测到碰撞，开始恢复");
        //     collision_state_ = CollisionRecoveryState::RECOVERING;
        // }

        break;
    }
    case CollisionRecoveryState::TURNING:
    {
        LOG_INFO("[Collision] 转向中");
        float turn_angle = GetTurnAngle(fusion_result);
        float turn_duration = std::abs(turn_angle) / collision_turn_speed_ * 1000;
        PublishVelocity(0.0, turn_angle > 0 ? collision_turn_speed_ : -collision_turn_speed_, turn_duration);

        if (is_collision_detected_)
        {
            LOG_INFO("[Collision] 检测到碰撞，转向恢复失败");
            collision_state_ = CollisionRecoveryState::BACKING_UP;
        }
        else
        {
            LOG_INFO("[Collision] 转向后未检测到碰撞，开始恢复");
            collision_state_ = CollisionRecoveryState::RECOVERING;

            // PublishVelocity(0.0, 0.0, 100); // 持续发送100ms
        }

        break;
    }
    case CollisionRecoveryState::RECOVERING:
    {
        LOG_INFO("[Collision] 恢复任务");
        collision_state_ = CollisionRecoveryState::NORMAL; // 恢复正常任务

        collision_recovery_completed_ = true;
        collision_recovery_succeed_ = true;
        break;
    }
    }
}

float NavigationBehaviorAlg::GetTurnAngle(const PerceptionFusionResult &fusion_result)
{
    // 优先使用障碍物状态信息决策
    const auto &obstacle = fusion_result.obstacle_result;

    // 情况1：明确单侧障碍物检测
    if (obstacle.left_obstacle_status == ObstacleDetectStatus::HAVE_OBSTACLE)
    {
        LOG_DEBUG("[Collision] 左侧检测到障碍物，向右转向");
        return -collision_turn_angle_; // 右转
    }
    if (obstacle.right_obstacle_status == ObstacleDetectStatus::HAVE_OBSTACLE)
    {
        LOG_DEBUG("[Collision] 右侧检测到障碍物，向左转向");
        return collision_turn_angle_; // 左转
    }

    // 情况2：随机转向决策（当所有传感器信息不明确时）
    const bool random_turn = (rand() % 2 == 0); // 50%概率
    LOG_DEBUG("[Collision] 无明确障碍物信息，随机转向方向: %s", random_turn ? "Right" : "Left");
    return random_turn ? -collision_turn_angle_ : collision_turn_angle_;
}

} // namespace fescue_iox
