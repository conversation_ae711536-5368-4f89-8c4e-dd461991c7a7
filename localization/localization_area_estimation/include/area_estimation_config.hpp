#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct AreaEstimationAlgConfig
{
    bool saveResult{false};                                                                       // default:0
    bool outputLog{false};                                                                        // default:0
    int useImuMode{3};                                                                            // default:3
    double bodyCompensation{0.165};                                                               // default:0.165
    std::string inner_config_path{"conf/localization_area_estimation_node/area_estimation.yaml"}; // 算法内部配置文件

    AreaEstimationAlgConfig() = default;
    AreaEstimationAlgConfig(const AreaEstimationAlgConfig &config) = default;
    ~AreaEstimationAlgConfig() = default;
    AreaEstimationAlgConfig &operator=(const AreaEstimationAlgConfig &conf);
    std::string toString() const;
};

bool operator==(const AreaEstimationAlgConfig &lhs, const AreaEstimationAlgConfig &rhs);
bool operator!=(const AreaEstimationAlgConfig &lhs, const AreaEstimationAlgConfig &rhs);

} // namespace fescue_iox
