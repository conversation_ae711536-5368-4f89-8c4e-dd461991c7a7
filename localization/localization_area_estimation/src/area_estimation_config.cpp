#include "area_estimation_config.hpp"

namespace fescue_iox
{

AreaEstimationAlgConfig &AreaEstimationAlgConfig::operator=(const AreaEstimationAlgConfig &conf)
{
    if (this != &conf)
    {
        this->saveResult = conf.saveResult;
        this->outputLog = conf.outputLog;
        this->useImuMode = conf.useImuMode;
        this->bodyCompensation = conf.bodyCompensation;
        this->inner_config_path = conf.inner_config_path;
    }

    return *this;
}

std::string AreaEstimationAlgConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------"
       << "\n";
    ss << "  saveResult: " << saveResult << "\n";
    ss << "  outputLog: " << outputLog << "\n";
    ss << "  useImuMode: " << useImuMode << "\n";
    ss << "  bodyCompensation: " << bodyCompensation << "\n";
    ss << "  inner_config_path: " << inner_config_path << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const AreaEstimationAlgConfig &lhs, const AreaEstimationAlgConfig &rhs)
{
    return lhs.saveResult == rhs.saveResult &&
           lhs.outputLog == rhs.outputLog &&
           lhs.useImuMode == rhs.useImuMode &&
           lhs.bodyCompensation == rhs.bodyCompensation &&
           lhs.inner_config_path == rhs.inner_config_path;
}

bool operator!=(const AreaEstimationAlgConfig &lhs, const AreaEstimationAlgConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<AreaEstimationAlgConfig>::LoadConfig(AreaEstimationAlgConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "AreaEstimationAlgConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "AreaEstimationAlgConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "AreaEstimationAlgConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    conf.saveResult = GetYamlValue<bool>(node, "saveResult", conf.saveResult);
    conf.outputLog = GetYamlValue<bool>(node, "outputLog", conf.outputLog);
    conf.useImuMode = GetYamlValue<int>(node, "useImuMode", conf.useImuMode);
    conf.bodyCompensation = GetYamlValue<double>(node, "bodyCompensation", conf.bodyCompensation);
    conf.inner_config_path = GetYamlValue<std::string>(node, "inner_config_path", conf.inner_config_path);

    return true;
}

template <>
bool Config<AreaEstimationAlgConfig>::CreateConfig(const AreaEstimationAlgConfig &conf, const std::string &conf_file)
{
    YAML::Node node;

    node["saveResult"] = conf.saveResult;
    node["outputLog"] = conf.outputLog;
    node["useImuMode"] = conf.useImuMode;
    node["bodyCompensation"] = conf.bodyCompensation;
    node["inner_config_path"] = conf.inner_config_path;

    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
