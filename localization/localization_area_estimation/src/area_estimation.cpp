#include "area_estimation.hpp"

#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"

namespace fescue_iox
{

void LocalizationAreaEstimationAlg::AreaEstimationResultCallback(const ob_area_estimation_result *result)
{
    LOG_DEBUG("ob_area_estimation_result, timestamp: {} area: {})",
              result->timestamp / 1000, result->area);
    LOG_DEBUG("ob_area_estimation_result, quaternion_w: {} quaternion_x: {} quaternion_y: {} quaternion_z: {})",
              result->quaternion_w, result->quaternion_x, result->quaternion_y, result->quaternion_z);
    LOG_DEBUG("ob_area_estimation_result, translation_x: {} translation_y: {} translation_z: {})",
              result->translation_x, result->translation_y, result->translation_z);

    alg_result_ = *result;
}

LocalizationAreaEstimationAlg::LocalizationAreaEstimationAlg(const std::string &conf_file)
    : conf_file_(conf_file)
{
    InitPublisher();
    InitAlgo();
}

LocalizationAreaEstimationAlg::~LocalizationAreaEstimationAlg()
{
    DeinitAlgo();
    LOG_WARN("LocalizationAreaEstimationAlg exit success!");
}

void LocalizationAreaEstimationAlg::DoMotorSpeedData(const mower_msgs::msg::McuMotorSpeed &data)
{
    if (started_.load())
    {
        uint64_t timestamp = data.system_timestamp * 1000000;
        OAE_add_ecode(alg_handle_, timestamp,
                      data.motor_speed_left, data.motor_speed_right);
    }
}

void LocalizationAreaEstimationAlg::DoImuData(const mower_msgs::msg::McuImu &data)
{
    if (started_.load())
    {
        uint64_t timestamp = data.system_timestamp * 1000000;
        OAE_add_IMU(alg_handle_, timestamp,
                    data.linear_acceleration_x, data.linear_acceleration_y, data.linear_acceleration_z,
                    data.angular_velocity_x, data.angular_velocity_y, data.angular_velocity_z);
    }
}

void LocalizationAreaEstimationAlg::InitAlgoParam()
{
    std::string conf_path = GetDirectoryPath(conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("LocalizationAreaEstimationAlg create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("LocalizationAreaEstimationAlg create config path failed!!!");
        }
    }
    if (!Config<AreaEstimationAlgConfig>::Init(conf_file_))
    {
        LOG_WARN("Init LocalizationAreaEstimationAlg config parameters failed!");
    }
    AreaEstimationAlgConfig config = Config<AreaEstimationAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    if (!Config<AreaEstimationAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set LocalizationAreaEstimationAlg config parameters failed!");
    }
}

void LocalizationAreaEstimationAlg::InitAlgo()
{
    InitAlgoParam();
    auto conf = Config<AreaEstimationAlgConfig>::GetConfig();
    auto result = OAE_create(&alg_handle_, conf.inner_config_path.c_str());
    if (AREA_OK != result)
    {
        LOG_ERROR("LocalizationAreaEstimationAlg create failed, error code: {:X}", result);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_LOCALIZATION_EREA_ESTIMATION_INIT_EXCEPTION);
        return;
    }
    Area_estimation_config param;
    param.saveResult = conf.saveResult;
    param.outputLog = conf.outputLog;
    param.useImuMode = conf.useImuMode;
    param.bodyCompensation = conf.bodyCompensation;
    OAE_set_config_parameter(alg_handle_, &param);
    OAE_start(alg_handle_);
    OAE_register_result_callback(alg_handle_, AreaEstimationResultCallback);
    LOG_INFO("localization area estimation alg version: {}", OAE_get_version(alg_handle_));
}

void LocalizationAreaEstimationAlg::InitPublisher()
{
    pub_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

const char *LocalizationAreaEstimationAlg::GetAlgVersion()
{
    return OAE_get_version(alg_handle_);
}

void LocalizationAreaEstimationAlg::DeinitAlgo()
{
    OAE_release(alg_handle_);
}

void LocalizationAreaEstimationAlg::StartAlgo()
{
    started_.store(true);
    OAE_clear(alg_handle_);
}

void LocalizationAreaEstimationAlg::StopAlgo()
{
    started_.store(false);
}

void LocalizationAreaEstimationAlg::InitAreaEstimation()
{
    StopAlgo();
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    StartAlgo();
}

bool LocalizationAreaEstimationAlg::GetAlgParam(Area_estimation_config &param)
{
    OAE_get_config_parameter(alg_handle_, &param);
    return true;
}

bool LocalizationAreaEstimationAlg::SetAlgParam(Area_estimation_config &param)
{
    OAE_set_config_parameter(alg_handle_, &param);
    return true;
}

void LocalizationAreaEstimationAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.timestamp = GetTimestampMs();
        exception.node_name = "localization_area_estimation_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception);
    }
}

} // namespace fescue_iox