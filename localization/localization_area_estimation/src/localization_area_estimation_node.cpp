#include "localization_area_estimation_node.hpp"

#include "mower_sdk_version.h"
#include "time.hpp"
#include "utils/dir.hpp"
#include "utils/file.hpp"
#include "utils/logger.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

LocalizationAreaEstimationNode::LocalizationAreaEstimationNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitHeartbeat();
    InitAlgorithms();
    InitSubscriber();
    InitServer();
}

LocalizationAreaEstimationNode::~LocalizationAreaEstimationNode()
{
    LOG_WARN("LocalizationAreaEstimationNode exit success!");
}

void LocalizationAreaEstimationNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void LocalizationAreaEstimationNode::InitParam()
{
    const std::string conf_file{"conf/localization_area_estimation_node/localization_area_estimation_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("localization_area_estimation_node create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("localization_area_estimation_node create config path failed!!!");
        }
    }
    if (!Config<LocalizationAreaEstimationNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init localization_area_estimation_node config parameters failed!");
    }
    LocalizationAreaEstimationNodeConfig config = Config<LocalizationAreaEstimationNodeConfig>::GetConfig();

    LOG_INFO("[localization_area_estimation_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[localization_area_estimation_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[localization_area_estimation_node] compile time: {}", _COMPILE_TIME_);
    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    area_estimation_conf_file_ = config.area_estimation_conf_file;

    if (!Config<LocalizationAreaEstimationNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set localization_area_estimation_node config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void LocalizationAreaEstimationNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void LocalizationAreaEstimationNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void LocalizationAreaEstimationNode::InitAlgorithms()
{
    area_estimation_alg_ = std::make_unique<LocalizationAreaEstimationAlg>(area_estimation_conf_file_);
}

void LocalizationAreaEstimationNode::InitSubscriber()
{
    sub_mcu_motor_speed_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>>(
        "mcu_motor_speed", 1, [this](const mower_msgs::msg::McuMotorSpeed &data, const std::string &event) {
            DealMotorSpeedData(data);
        });

    sub_mcu_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuImu>>(
        "mcu_imu", 1, [this](const mower_msgs::msg::McuImu &data, const std::string &event) {
            DealImuData(data);
        });

    sub_algo_ctrl_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>>(
        "perception_localization_alg_ctrl", 5, [this](const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data, const std::string &event) {
            DealAlgCtrl(data);
        });
}

void LocalizationAreaEstimationNode::InitServer()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_localization_area_estimation_node_param", 1, [this](const get_node_param_request &request, get_node_param_response &response) {
            response.success = DealGetNodeParam(response.data);
            LOG_INFO("Get localization area sstimation node param execute {}", response.success);
        });

    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_localization_area_estimation_node_param", 1, [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = DealSetNodeParam(request.data);
            LOG_INFO("Set localization area sstimation node param execute {}", response.success);
        });

    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_localization_area_estimation_alg_param", 1, [this](const get_alg_param_request &request, get_alg_param_response &response) {
            response.success = DealGetAlgParam(response.data);
            LOG_INFO("Get localization area sstimation alg param execute {}", response.success);
        });

    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_localization_area_estimation_alg_param", 1, [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = DealSetAlgParam(request.data);
            LOG_INFO("Set localization area sstimation alg param execute {}", response.success);
        });

    service_area_calculation_start_ = std::make_unique<IceoryxServerMower<area_calc_start_request, area_calc_start_response>>(
        "localization_area_calculation_start", 1, [this](const area_calc_start_request &request, area_calc_start_response &response) {
            response.success = StartAreaCalculation();
            response.timestamp = GetTimestampMs();
            LOG_INFO("Start area calculation execute {}", response.success);
        });

    service_area_calculation_stop_ = std::make_unique<IceoryxServerMower<area_calc_stop_request, area_calc_stop_response>>(
        "localization_area_calculation_stop", 1, [this](const area_calc_stop_request &request, area_calc_stop_response &response) {
            response.success = StopAreaCalculation(response.result);
            response.timestamp = GetTimestampMs();
            LOG_INFO("Stop area calculation execute {}", response.success);
        });

    service_get_algo_version_ = std::make_unique<IceoryxServerMower<get_alg_version_request, get_alg_version_response>>(
        "get_localization_area_estimation_algo_version", 1, [this](const get_alg_version_request &request, get_alg_version_response &response) {
            response.success = DealGetAlgVersion(response.data);
            response.timestamp = GetTimestampMs();
            LOG_INFO("Get localization area estimation algo version execute {}", response.success);
        });
}

void LocalizationAreaEstimationNode::DealMotorSpeedData(const mower_msgs::msg::McuMotorSpeed &data)
{
    if (!area_estimation_alg_enable_)
    {
        LOG_DEBUG("Localization area sstimation algo is disabled by config!");
        return;
    }

    if (area_estimation_alg_)
    {
        area_estimation_alg_->DoMotorSpeedData(data);
    }
}

void LocalizationAreaEstimationNode::DealImuData(const mower_msgs::msg::McuImu &data)
{
    if (!area_estimation_alg_enable_)
    {
        LOG_DEBUG("Localization area sstimation algo is disabled by config!");
        return;
    }

    if (area_estimation_alg_)
    {
        area_estimation_alg_->DoImuData(data);
    }
}

bool LocalizationAreaEstimationNode::StartAreaCalculation()
{
    if (!area_estimation_alg_enable_)
    {
        LOG_WARN("Start area calculation fail, localization area estimation algo is disabled!");
        return false;
    }

    if (!area_estimation_alg_)
    {
        LOG_WARN("Start area calculation fail, area_estimation_alg_ is nullptr!");
        return false;
    }

    area_estimation_alg_->StopAlgo();
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    area_estimation_alg_->StartAlgo();
    return true;
}

bool LocalizationAreaEstimationNode::StopAreaCalculation(mower_msgs::srv::WheelOdomAreaResult &odom_result)
{
    if (!area_estimation_alg_enable_)
    {
        LOG_WARN("Stop area calculation fail, localization area estimation algo is disabled!");
        return false;
    }

    if (!area_estimation_alg_)
    {
        LOG_WARN("Stop area calculation fail, area_estimation_alg_ is nullptr!");
        return false;
    }

    area_estimation_alg_->StopAlgo();
    auto result = area_estimation_alg_->GetAreaEstimationResult();
    odom_result.area = result.area;
    odom_result.perimeter = result.perimeter;
    return true;
}

bool LocalizationAreaEstimationNode::DealGetAlgVersion(ob_mower_srvs::AlgoVersionDataVect &data)
{
    if (area_estimation_alg_)
    {
        ob_mower_srvs::AlgoVersionData alg_version_data;
        alg_version_data.algo_name.unsafe_assign("localization_area_estimation");
        alg_version_data.version.unsafe_assign(area_estimation_alg_->GetAlgVersion());
        data.version_data_vect.push_back(alg_version_data);
    }
    return true;
}

void LocalizationAreaEstimationNode::DealAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &data)
{
    using namespace ob_mower_msgs;
    for (size_t i = 0; i < data.ctrl_list.size(); i++)
    {
        if (data.ctrl_list[i].alg_type == PerceptionLocalizationAlgType::LOCALIZATION_AREA_ESTIMATION)
        {
            area_estimation_alg_enable_ = (data.ctrl_list[i].alg_state == PerceptionLocalizationAlgState::ENABLE ? true : false);
            LOG_WARN("{} {}", asStringLiteral(data.ctrl_list[i].alg_type).c_str(), asStringLiteral(data.ctrl_list[i].alg_state).c_str());
            break;
        }
    }
}

bool LocalizationAreaEstimationNode::DealGetNodeParam(ob_mower_srvs::NodeParamData &data)
{
    LocalizationAreaEstimationNodeConfig config = Config<LocalizationAreaEstimationNodeConfig>::GetConfig();
    data.console_log_level.unsafe_assign(config.common_conf.console_log_level.c_str());
    data.file_log_level.unsafe_assign(config.common_conf.file_log_level.c_str());
    return true;
}

bool LocalizationAreaEstimationNode::DealSetNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    LocalizationAreaEstimationNodeConfig config = Config<LocalizationAreaEstimationNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    Config<LocalizationAreaEstimationNodeConfig>::SetConfig(config);
    LOG_INFO("New LocalizationAreaEstimationNodeConfig params: {}", config.toString().c_str());
    return true;
}

bool LocalizationAreaEstimationNode::DealSetAlgParam(const ob_mower_srvs::LocAreaEstimateAlgParam &data)
{
    if (!area_estimation_alg_)
    {
        return false;
    }

    Area_estimation_config param;
    area_estimation_alg_->GetAlgParam(param);
    param.saveResult = data.saveResult;
    param.outputLog = data.outputLog;
    param.useImuMode = data.useImuMode;
    param.bodyCompensation = data.bodyCompensation;
    return area_estimation_alg_->SetAlgParam(param);
}

bool LocalizationAreaEstimationNode::DealGetAlgParam(ob_mower_srvs::LocAreaEstimateAlgParam &data)
{
    if (!area_estimation_alg_)
    {
        return false;
    }

    Area_estimation_config param;
    area_estimation_alg_->GetAlgParam(param);
    data.saveResult = param.saveResult;
    data.outputLog = param.outputLog;
    data.useImuMode = param.useImuMode;
    data.bodyCompensation = param.bodyCompensation;
    return true;
}

} // namespace fescue_iox
