#include "localization_area_estimation_node_config.hpp"

#include <fstream>
#include <sstream>
#include <typeinfo>

namespace fescue_iox
{
LocalizationAreaEstimationNodeConfig &LocalizationAreaEstimationNodeConfig::operator=(const LocalizationAreaEstimationNodeConfig &conf)
{
    if (this != &conf)
    {
        this->common_conf = conf.common_conf;
        this->area_estimation_conf_file = conf.area_estimation_conf_file;
    }
    return *this;
}

std::string LocalizationAreaEstimationNodeConfig::toString() const
{
    std::stringstream ss;
    ss << "------------------------- " << typeid(*this).name() << " -------------------------"
       << "\n";
    ss << "  log_dir: " << common_conf.log_dir << "\n";
    ss << "  console_log_level: " << common_conf.console_log_level << "\n";
    ss << "  file_log_level: " << common_conf.file_log_level << "\n";
    ss << "  area_estimation_conf_file: " << area_estimation_conf_file << "\n";
    ss << "------------------------- " << typeid(*this).name() << " -------------------------";
    return ss.str();
}

bool operator==(const LocalizationAreaEstimationNodeConfig &lhs, const LocalizationAreaEstimationNodeConfig &rhs)
{
    return lhs.common_conf == rhs.common_conf &&
           lhs.area_estimation_conf_file == rhs.area_estimation_conf_file;
}

bool operator!=(const LocalizationAreaEstimationNodeConfig &lhs, const LocalizationAreaEstimationNodeConfig &rhs)
{
    return !(lhs == rhs);
}

template <>
bool Config<LocalizationAreaEstimationNodeConfig>::LoadConfig(LocalizationAreaEstimationNodeConfig &conf, const std::string &conf_file)
{
    YAML::Node node;
    try
    {
        node = YAML::LoadFile(conf_file);
    }
    catch (const YAML::BadFile &e)
    {
        std::cerr << "LocalizationAreaEstimationNodeConfig load config fail (BadFile): " << e.what() << '\n';
        return false;
    }
    catch (const YAML::ParserException &e)
    {
        std::cerr << "LocalizationAreaEstimationNodeConfig load config fail (ParserException): " << e.what() << '\n';
        return false;
    }
    catch (const std::exception &e)
    {
        std::cerr << "LocalizationAreaEstimationNodeConfig load config fail (Unknown): " << e.what() << '\n';
        return false;
    }

    conf.common_conf.log_dir = GetYamlValue<std::string>(node, "log_dir", conf.common_conf.log_dir);
    conf.common_conf.console_log_level = GetYamlValue<std::string>(node, "console_log_level", conf.common_conf.console_log_level);
    conf.common_conf.file_log_level = GetYamlValue<std::string>(node, "file_log_level", conf.common_conf.file_log_level);
    conf.area_estimation_conf_file = GetYamlValue<std::string>(node, "area_estimation_conf_file", conf.area_estimation_conf_file);

    return true;
}

template <>
bool Config<LocalizationAreaEstimationNodeConfig>::CreateConfig(const LocalizationAreaEstimationNodeConfig &conf, const std::string &conf_file)
{
    YAML::Node node;

    node["log_dir"] = conf.common_conf.log_dir;
    node["console_log_level"] = conf.common_conf.console_log_level;
    node["file_log_level"] = conf.common_conf.file_log_level;
    node["area_estimation_conf_file"] = conf.area_estimation_conf_file;

    return WriteYamlFile(conf_file, node);
}

} // namespace fescue_iox
